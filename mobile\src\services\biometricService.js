import { authService } from './authService';

class BiometricService {
  async getLatestReadings() {
    return authService.makeRequest('/biometric/latest');
  }

  async addReading(readingData) {
    return authService.makeRequest('/biometric', {
      method: 'POST',
      body: JSON.stringify(readingData),
    });
  }

  async getReadings(filters = {}) {
    const queryParams = new URLSearchParams(filters).toString();
    return authService.makeRequest(`/biometric?${queryParams}`);
  }

  async getTrends(measurementType, period = 'month') {
    return authService.makeRequest(`/biometric/trends?measurement_type=${measurementType}&period=${period}`);
  }

  async getWeeklyTrends() {
    // Mock data for weekly trends
    return [75, 78, 82, 79, 85, 88, 86];
  }

  async getEmergencyReadings() {
    return authService.makeRequest('/biometric/emergency');
  }
}

export const biometricService = new BiometricService();
