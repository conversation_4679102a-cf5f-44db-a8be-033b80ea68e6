import React, { useEffect } from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
} from 'react-native';
import {
  Text,
  ActivityIndicator,
} from 'react-native-paper';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import * as Animatable from 'react-native-animatable';

const { width, height } = Dimensions.get('window');

const SplashScreen = () => {
  return (
    <LinearGradient
      colors={['#00BCD4', '#4CAF50']}
      style={styles.container}
    >
      <View style={styles.content}>
        <Animatable.View 
          animation="bounceIn" 
          duration={1500}
          style={styles.logoContainer}
        >
          <Icon name="heart-pulse" size={120} color="#FFFFFF" />
        </Animatable.View>
        
        <Animatable.View 
          animation="fadeInUp" 
          duration={1000}
          delay={500}
          style={styles.textContainer}
        >
          <Text style={styles.title}>HealthVision AI</Text>
          <Text style={styles.subtitle}>
            Your Personal Health Assistant
          </Text>
          <Text style={styles.tagline}>
            Powered by Artificial Intelligence
          </Text>
        </Animatable.View>
        
        <Animatable.View 
          animation="fadeIn" 
          duration={1000}
          delay={1000}
          style={styles.loadingContainer}
        >
          <ActivityIndicator size="large" color="#FFFFFF" />
          <Text style={styles.loadingText}>Loading...</Text>
        </Animatable.View>
      </View>
      
      <Animatable.View 
        animation="fadeInUp" 
        duration={1000}
        delay={1500}
        style={styles.footer}
      >
        <Text style={styles.footerText}>
          The Future of Personalized Healthcare
        </Text>
        <Text style={styles.versionText}>
          Version 1.0.0
        </Text>
      </Animatable.View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  logoContainer: {
    marginBottom: 40,
  },
  textContainer: {
    alignItems: 'center',
    marginBottom: 60,
  },
  title: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 12,
    letterSpacing: 1,
  },
  subtitle: {
    fontSize: 18,
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 8,
    opacity: 0.9,
  },
  tagline: {
    fontSize: 14,
    color: '#FFFFFF',
    textAlign: 'center',
    opacity: 0.8,
    fontStyle: 'italic',
  },
  loadingContainer: {
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#FFFFFF',
    marginTop: 16,
    opacity: 0.8,
  },
  footer: {
    position: 'absolute',
    bottom: 50,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 14,
    color: '#FFFFFF',
    textAlign: 'center',
    opacity: 0.8,
    marginBottom: 8,
  },
  versionText: {
    fontSize: 12,
    color: '#FFFFFF',
    textAlign: 'center',
    opacity: 0.6,
  },
});

export default SplashScreen;
