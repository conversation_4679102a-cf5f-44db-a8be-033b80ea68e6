const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');

const healthPredictionSchema = new mongoose.Schema({
  prediction_id: {
    type: String,
    default: uuidv4,
    unique: true,
    required: true
  },
  user_id: {
    type: String,
    required: true,
    ref: 'User'
  },
  prediction_type: {
    type: String,
    enum: [
      'disease_risk',
      'wellness_score',
      'lifestyle_recommendation',
      'medication_adherence',
      'health_trend',
      'emergency_prediction',
      'fitness_goal_achievement',
      'sleep_quality_prediction',
      'stress_level_forecast',
      'nutrition_recommendation'
    ],
    required: true
  },
  condition_name: {
    type: String,
    required: true,
    trim: true
  },
  risk_score: {
    type: Number,
    required: true,
    min: 0,
    max: 100
  },
  confidence_level: {
    type: Number,
    required: true,
    min: 0,
    max: 100
  },
  prediction_date: {
    type: Date,
    default: Date.now,
    required: true
  },
  prediction_horizon: {
    type: String,
    enum: ['1_week', '1_month', '3_months', '6_months', '1_year', '5_years'],
    required: true
  },
  factors_considered: {
    biometric_data: [{
      measurement_type: String,
      weight: Number,
      trend: String
    }],
    lifestyle_factors: [{
      factor: String,
      impact: String,
      weight: Number
    }],
    genetic_factors: [{
      gene: String,
      variant: String,
      impact: String
    }],
    environmental_factors: [{
      factor: String,
      impact: String
    }]
  },
  recommendations: {
    immediate_actions: [{
      action: String,
      priority: {
        type: String,
        enum: ['low', 'medium', 'high', 'critical']
      },
      estimated_impact: Number
    }],
    lifestyle_changes: [{
      category: String,
      change: String,
      timeline: String,
      difficulty: String
    }],
    medical_consultation: {
      recommended: Boolean,
      urgency: String,
      specialist_type: String,
      reason: String
    },
    monitoring_plan: [{
      metric: String,
      frequency: String,
      target_range: String
    }]
  },
  model_version: {
    type: String,
    required: true
  },
  model_metadata: {
    algorithm: String,
    training_data_size: Number,
    accuracy_metrics: {
      precision: Number,
      recall: Number,
      f1_score: Number,
      auc_roc: Number
    },
    feature_importance: [{
      feature: String,
      importance: Number
    }]
  },
  is_reviewed_by_doctor: {
    type: Boolean,
    default: false
  },
  doctor_review: {
    reviewed_by: {
      type: String,
      ref: 'HealthcareProvider'
    },
    review_date: Date,
    doctor_notes: String,
    agreement_level: {
      type: String,
      enum: ['strongly_agree', 'agree', 'neutral', 'disagree', 'strongly_disagree']
    },
    modifications: String
  },
  user_feedback: {
    usefulness_rating: {
      type: Number,
      min: 1,
      max: 5
    },
    followed_recommendations: Boolean,
    outcome_reported: String,
    feedback_date: Date
  },
  status: {
    type: String,
    enum: ['active', 'outdated', 'superseded', 'archived'],
    default: 'active'
  },
  related_predictions: [{
    type: String,
    ref: 'HealthPrediction'
  }]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
healthPredictionSchema.index({ user_id: 1, prediction_date: -1 });
healthPredictionSchema.index({ user_id: 1, prediction_type: 1 });
healthPredictionSchema.index({ risk_score: -1 });
healthPredictionSchema.index({ status: 1 });
healthPredictionSchema.index({ 'recommendations.medical_consultation.recommended': 1 });

// Virtual for risk category
healthPredictionSchema.virtual('risk_category').get(function() {
  if (this.risk_score >= 80) return 'Very High';
  if (this.risk_score >= 60) return 'High';
  if (this.risk_score >= 40) return 'Moderate';
  if (this.risk_score >= 20) return 'Low';
  return 'Very Low';
});

// Virtual for confidence category
healthPredictionSchema.virtual('confidence_category').get(function() {
  if (this.confidence_level >= 90) return 'Very High';
  if (this.confidence_level >= 75) return 'High';
  if (this.confidence_level >= 60) return 'Moderate';
  if (this.confidence_level >= 45) return 'Low';
  return 'Very Low';
});

// Static method to get latest predictions for user
healthPredictionSchema.statics.getLatestPredictions = function(userId, limit = 10) {
  return this.find({
    user_id: userId,
    status: 'active'
  })
  .sort({ prediction_date: -1 })
  .limit(limit);
};

// Static method to get high-risk predictions
healthPredictionSchema.statics.getHighRiskPredictions = function(userId) {
  return this.find({
    user_id: userId,
    risk_score: { $gte: 60 },
    status: 'active'
  }).sort({ risk_score: -1 });
};

// Static method to get predictions requiring medical consultation
healthPredictionSchema.statics.getPredictionsRequiringConsultation = function(userId) {
  return this.find({
    user_id: userId,
    'recommendations.medical_consultation.recommended': true,
    status: 'active'
  }).sort({ risk_score: -1 });
};

// Method to check if prediction is expired
healthPredictionSchema.methods.isExpired = function() {
  const now = new Date();
  const predictionAge = now - this.prediction_date;
  
  // Define expiry times based on prediction horizon
  const expiryTimes = {
    '1_week': 7 * 24 * 60 * 60 * 1000,
    '1_month': 30 * 24 * 60 * 60 * 1000,
    '3_months': 90 * 24 * 60 * 60 * 1000,
    '6_months': 180 * 24 * 60 * 60 * 1000,
    '1_year': 365 * 24 * 60 * 60 * 1000,
    '5_years': 5 * 365 * 24 * 60 * 60 * 1000
  };

  const expiryTime = expiryTimes[this.prediction_horizon] || expiryTimes['1_month'];
  return predictionAge > expiryTime;
};

// Method to get actionable recommendations
healthPredictionSchema.methods.getActionableRecommendations = function() {
  return {
    immediate: this.recommendations.immediate_actions.filter(action => 
      action.priority === 'high' || action.priority === 'critical'
    ),
    lifestyle: this.recommendations.lifestyle_changes.filter(change => 
      change.difficulty !== 'very_hard'
    ),
    medical: this.recommendations.medical_consultation.recommended ? 
      this.recommendations.medical_consultation : null
  };
};

// Pre-save middleware to update status if expired
healthPredictionSchema.pre('save', function(next) {
  if (this.isExpired() && this.status === 'active') {
    this.status = 'outdated';
  }
  next();
});

module.exports = mongoose.model('HealthPrediction', healthPredictionSchema);
