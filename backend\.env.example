# HealthVision AI Backend Environment Configuration

# Server Configuration
NODE_ENV=development
PORT=5000
API_URL=http://localhost:5000

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/healthvision_ai

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_complex
JWT_EXPIRES_IN=7d

# Frontend Configuration
FRONTEND_URL=http://localhost:3000

# Email Configuration (for notifications and password reset)
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
EMAIL_FROM=HealthVision AI <<EMAIL>>

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********

# Cloud Storage (Cloudinary)
CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret

# Firebase Configuration (for push notifications)
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_PRIVATE_KEY_ID=your_firebase_private_key_id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour_firebase_private_key\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=your_firebase_client_id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token

# AI/ML Configuration
TENSORFLOW_MODEL_PATH=./models/health_prediction_model.json
ML_API_ENDPOINT=http://localhost:8000/predict

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security
BCRYPT_SALT_ROUNDS=12
SESSION_SECRET=your_session_secret_here

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Health Monitoring
HEALTH_CHECK_INTERVAL=30000
EMERGENCY_ALERT_THRESHOLD=5

# External APIs
WEATHER_API_KEY=your_weather_api_key
NUTRITION_API_KEY=your_nutrition_api_key
FITNESS_API_KEY=your_fitness_api_key

# Development Tools
SWAGGER_ENABLED=true
DEBUG_MODE=true
MOCK_DATA_ENABLED=false

# Production Settings (uncomment for production)
# NODE_ENV=production
# DEBUG_MODE=false
# SWAGGER_ENABLED=false
# RATE_LIMIT_MAX_REQUESTS=50
