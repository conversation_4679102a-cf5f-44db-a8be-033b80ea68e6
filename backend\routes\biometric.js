const express = require('express');
const { body, query, validationResult } = require('express-validator');
const BiometricData = require('../models/BiometricData');
const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     BiometricData:
 *       type: object
 *       required:
 *         - measurement_type
 *         - value
 *         - unit
 *       properties:
 *         measurement_type:
 *           type: string
 *           enum: [heart_rate, blood_pressure_systolic, blood_pressure_diastolic, glucose, temperature, oxygen_saturation, weight, body_fat_percentage, muscle_mass, steps, calories_burned, sleep_duration, sleep_quality, stress_level, hydration_level]
 *         value:
 *           type: number
 *           minimum: 0
 *         unit:
 *           type: string
 *         device_id:
 *           type: string
 *         notes:
 *           type: string
 *         is_manual_entry:
 *           type: boolean
 */

/**
 * @swagger
 * /api/biometric:
 *   get:
 *     summary: Get user's biometric data
 *     tags: [Biometric Data]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: measurement_type
 *         schema:
 *           type: string
 *         description: Filter by measurement type
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date for filtering
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         description: End date for filtering
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Number of records to return
 *     responses:
 *       200:
 *         description: Biometric data retrieved successfully
 */
router.get('/', [
  query('measurement_type').optional().isIn([
    'heart_rate', 'blood_pressure_systolic', 'blood_pressure_diastolic',
    'glucose', 'temperature', 'oxygen_saturation', 'weight',
    'body_fat_percentage', 'muscle_mass', 'steps', 'calories_burned',
    'sleep_duration', 'sleep_quality', 'stress_level', 'hydration_level'
  ]),
  query('start_date').optional().isISO8601(),
  query('end_date').optional().isISO8601(),
  query('limit').optional().isInt({ min: 1, max: 1000 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const {
      measurement_type,
      start_date,
      end_date,
      limit = 50
    } = req.query;

    // Build query
    const query = { user_id: req.user.userId };

    if (measurement_type) {
      query.measurement_type = measurement_type;
    }

    if (start_date || end_date) {
      query.timestamp = {};
      if (start_date) query.timestamp.$gte = new Date(start_date);
      if (end_date) query.timestamp.$lte = new Date(end_date);
    }

    const biometricData = await BiometricData.find(query)
      .sort({ timestamp: -1 })
      .limit(parseInt(limit));

    // Get summary statistics
    const summary = await BiometricData.aggregate([
      { $match: { user_id: req.user.userId } },
      {
        $group: {
          _id: '$measurement_type',
          count: { $sum: 1 },
          latest_value: { $first: '$value' },
          latest_timestamp: { $first: '$timestamp' },
          avg_value: { $avg: '$value' },
          min_value: { $min: '$value' },
          max_value: { $max: '$value' }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    res.json({
      success: true,
      message: 'Biometric data retrieved successfully',
      data: {
        readings: biometricData,
        summary,
        total_count: biometricData.length
      }
    });

  } catch (error) {
    console.error('Get biometric data error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/biometric:
 *   post:
 *     summary: Add new biometric reading
 *     tags: [Biometric Data]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/BiometricData'
 *     responses:
 *       201:
 *         description: Biometric data added successfully
 */
router.post('/', [
  body('measurement_type').isIn([
    'heart_rate', 'blood_pressure_systolic', 'blood_pressure_diastolic',
    'glucose', 'temperature', 'oxygen_saturation', 'weight',
    'body_fat_percentage', 'muscle_mass', 'steps', 'calories_burned',
    'sleep_duration', 'sleep_quality', 'stress_level', 'hydration_level'
  ]),
  body('value').isNumeric().custom(value => value >= 0),
  body('unit').isIn([
    'bpm', 'mmHg', 'mg/dL', 'mmol/L', '°C', '°F', '%', 'kg', 'lbs',
    'steps', 'kcal', 'hours', 'minutes', 'score', 'liters', 'ml'
  ]),
  body('device_id').optional().isString(),
  body('notes').optional().isLength({ max: 500 }),
  body('is_manual_entry').optional().isBoolean(),
  body('timestamp').optional().isISO8601()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const biometricData = new BiometricData({
      ...req.body,
      user_id: req.user.userId,
      timestamp: req.body.timestamp || new Date()
    });

    await biometricData.save();

    // Emit real-time update via Socket.IO
    const io = req.app.locals.io;
    if (io) {
      io.to(`user_${req.user.userId}`).emit('biometric_update', {
        type: 'new_reading',
        data: biometricData
      });

      // Check for emergency alerts
      if (biometricData.flags.is_emergency) {
        io.to(`user_${req.user.userId}`).emit('emergency_alert', {
          type: 'critical_reading',
          measurement_type: biometricData.measurement_type,
          value: biometricData.value,
          timestamp: biometricData.timestamp
        });
      }
    }

    res.status(201).json({
      success: true,
      message: 'Biometric data added successfully',
      data: biometricData
    });

  } catch (error) {
    console.error('Add biometric data error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/biometric/latest:
 *   get:
 *     summary: Get latest readings for each measurement type
 *     tags: [Biometric Data]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Latest readings retrieved successfully
 */
router.get('/latest', async (req, res) => {
  try {
    const latestReadings = await BiometricData.aggregate([
      { $match: { user_id: req.user.userId } },
      { $sort: { timestamp: -1 } },
      {
        $group: {
          _id: '$measurement_type',
          latest_reading: { $first: '$$ROOT' }
        }
      },
      { $replaceRoot: { newRoot: '$latest_reading' } },
      { $sort: { timestamp: -1 } }
    ]);

    res.json({
      success: true,
      message: 'Latest readings retrieved successfully',
      data: latestReadings
    });

  } catch (error) {
    console.error('Get latest readings error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

/**
 * @swagger
 * /api/biometric/trends:
 *   get:
 *     summary: Get biometric trends and analytics
 *     tags: [Biometric Data]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: measurement_type
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [week, month, quarter, year]
 *           default: month
 *     responses:
 *       200:
 *         description: Trends data retrieved successfully
 */
router.get('/trends', [
  query('measurement_type').isIn([
    'heart_rate', 'blood_pressure_systolic', 'blood_pressure_diastolic',
    'glucose', 'temperature', 'oxygen_saturation', 'weight',
    'body_fat_percentage', 'muscle_mass', 'steps', 'calories_burned',
    'sleep_duration', 'sleep_quality', 'stress_level', 'hydration_level'
  ]),
  query('period').optional().isIn(['week', 'month', 'quarter', 'year'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const { measurement_type, period = 'month' } = req.query;

    // Calculate date range based on period
    const now = new Date();
    const periodDays = {
      week: 7,
      month: 30,
      quarter: 90,
      year: 365
    };

    const startDate = new Date(now.getTime() - (periodDays[period] * 24 * 60 * 60 * 1000));

    const trendsData = await BiometricData.getReadingsInRange(
      req.user.userId,
      measurement_type,
      startDate,
      now
    );

    // Calculate trend statistics
    const values = trendsData.map(reading => reading.value);
    const trend = {
      current_value: values[values.length - 1] || null,
      average: values.length > 0 ? values.reduce((a, b) => a + b, 0) / values.length : 0,
      min: Math.min(...values),
      max: Math.max(...values),
      trend_direction: values.length > 1 ? 
        (values[values.length - 1] > values[0] ? 'increasing' : 'decreasing') : 'stable',
      data_points: trendsData.length
    };

    res.json({
      success: true,
      message: 'Trends data retrieved successfully',
      data: {
        measurement_type,
        period,
        trend_analysis: trend,
        readings: trendsData
      }
    });

  } catch (error) {
    console.error('Get trends error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

/**
 * @swagger
 * /api/biometric/emergency:
 *   get:
 *     summary: Get emergency/critical readings
 *     tags: [Biometric Data]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Emergency readings retrieved successfully
 */
router.get('/emergency', async (req, res) => {
  try {
    const emergencyReadings = await BiometricData.getEmergencyReadings(req.user.userId);

    res.json({
      success: true,
      message: 'Emergency readings retrieved successfully',
      data: emergencyReadings
    });

  } catch (error) {
    console.error('Get emergency readings error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

module.exports = router;
