const express = require('express');
const router = express.Router();

// GET /api/challenges - Get available challenges
router.get('/', async (req, res) => {
  try {
    const challenges = [
      {
        challenge_id: 'challenge_1',
        challenge_name: '10,000 Steps Daily',
        description: 'Walk 10,000 steps every day for a week',
        challenge_type: 'steps',
        target_value: 10000,
        duration_days: 7,
        reward_points: 100,
        difficulty_level: 'medium'
      },
      {
        challenge_id: 'challenge_2',
        challenge_name: 'Hydration Hero',
        description: 'Drink 8 glasses of water daily',
        challenge_type: 'hydration',
        target_value: 8,
        duration_days: 14,
        reward_points: 150,
        difficulty_level: 'easy'
      }
    ];

    res.json({
      success: true,
      message: 'Challenges retrieved successfully',
      data: challenges
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

module.exports = router;
