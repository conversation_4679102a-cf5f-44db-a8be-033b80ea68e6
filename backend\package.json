{"name": "healthvision-ai-backend", "version": "1.0.0", "description": "HealthVision AI Backend", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "socket.io": "^4.7.2", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1", "compression": "^1.7.4", "swagger-ui-express": "^5.0.0", "swagger-jsdoc": "^6.2.8", "moment": "^2.29.4", "uuid": "^9.0.0"}, "devDependencies": {"nodemon": "^3.0.1"}}