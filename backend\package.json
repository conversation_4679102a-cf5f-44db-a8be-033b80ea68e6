{"name": "healthvision-ai-backend", "version": "1.0.0", "description": "HealthVision AI - Biometric Health Prediction & Wellness Ecosystem Backend", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "seed": "node scripts/seedDatabase.js"}, "keywords": ["health", "biometric", "AI", "wellness", "telemedicine", "healthcare"], "author": "HealthVision AI Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "multer": "^1.4.5-lts.1", "socket.io": "^4.7.2", "node-cron": "^3.0.2", "nodemailer": "^6.9.4", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1", "compression": "^1.7.4", "swagger-ui-express": "^5.0.0", "swagger-jsdoc": "^6.2.8", "@tensorflow/tfjs-node": "^4.10.0", "ml-matrix": "^6.10.4", "simple-statistics": "^7.8.3", "moment": "^2.29.4", "uuid": "^9.0.0", "joi": "^17.9.2", "cloudinary": "^1.40.0", "twilio": "^4.15.0", "firebase-admin": "^11.10.1"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3", "@types/jest": "^29.5.4"}, "engines": {"node": ">=16.0.0"}}