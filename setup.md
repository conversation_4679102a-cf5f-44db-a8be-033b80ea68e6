# 🚀 HealthVision AI - Complete Setup Guide

## 📋 Prerequisites

Before setting up the project, ensure you have the following installed:

### Required Software
- **Node.js** (v16 or higher) - [Download](https://nodejs.org/)
- **MongoDB** (v5.0 or higher) - [Download](https://www.mongodb.com/try/download/community)
- **React Native CLI** - `npm install -g react-native-cli`
- **Android Studio** (for Android development)
- **Xcode** (for iOS development - macOS only)

### Development Tools
- **Git** - [Download](https://git-scm.com/)
- **VS Code** (recommended) - [Download](https://code.visualstudio.com/)
- **Postman** (for API testing) - [Download](https://www.postman.com/)

## 🏗️ Project Setup

### 1. Clone and Setup Backend

```bash
# Navigate to backend directory
cd backend

# Install dependencies
npm install

# Copy environment file
cp .env.example .env

# Edit .env file with your configuration
# Update MongoDB URI, JWT secret, and other settings

# Start MongoDB service
# On Windows: net start MongoDB
# On macOS: brew services start mongodb-community
# On Linux: sudo systemctl start mongod

# Seed the database with sample data
npm run seed

# Start the backend server
npm run dev
```

The backend will be available at `http://localhost:5000`

### 2. Setup Mobile App

```bash
# Navigate to mobile directory
cd mobile

# Install dependencies
npm install

# For iOS (macOS only)
cd ios && pod install && cd ..

# Start Metro bundler
npm start

# In a new terminal, run the app
# For Android:
npm run android

# For iOS:
npm run ios
```

## 🔧 Configuration

### Backend Configuration

Edit `backend/.env` file:

```env
# Database
MONGODB_URI=mongodb://localhost:27017/healthvision_ai

# JWT Secret (generate a strong secret)
JWT_SECRET=your_super_secret_jwt_key_here

# API URL
API_URL=http://localhost:5000
FRONTEND_URL=http://localhost:3000
```

### Mobile App Configuration

Create `mobile/src/config/api.js`:

```javascript
export const API_CONFIG = {
  BASE_URL: 'http://localhost:5000/api',
  SOCKET_URL: 'http://localhost:5000',
  TIMEOUT: 10000,
};
```

## 📱 Mobile Development Setup

### Android Setup

1. **Install Android Studio**
2. **Setup Android SDK**
3. **Create Virtual Device (AVD)**
4. **Enable Developer Options on physical device**

```bash
# Check Android setup
npx react-native doctor

# Run on Android
npm run android
```

### iOS Setup (macOS only)

1. **Install Xcode from App Store**
2. **Install Xcode Command Line Tools**
3. **Install CocoaPods**: `sudo gem install cocoapods`

```bash
# Install iOS dependencies
cd ios && pod install && cd ..

# Run on iOS
npm run ios
```

## 🗄️ Database Setup

### MongoDB Setup

1. **Install MongoDB Community Edition**
2. **Start MongoDB service**
3. **Create database and collections**

```bash
# Connect to MongoDB
mongo

# Create database
use healthvision_ai

# The collections will be created automatically when you run the app
```

### Sample Data

Run the seed script to populate the database with sample data:

```bash
cd backend
npm run seed
```

## 🧪 Testing

### Backend Testing

```bash
cd backend

# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Test specific endpoints with Postman
# Import the Postman collection from /docs/postman/
```

### Mobile Testing

```bash
cd mobile

# Run unit tests
npm test

# Run E2E tests (if configured)
npm run test:e2e
```

## 📚 API Documentation

Once the backend is running, visit:
- **Swagger UI**: `http://localhost:5000/api-docs`
- **Health Check**: `http://localhost:5000/health`

## 🔐 Security Setup

### JWT Configuration

Generate a strong JWT secret:

```bash
# Generate random secret
node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
```

### Environment Security

- Never commit `.env` files
- Use different secrets for development and production
- Enable HTTPS in production
- Configure CORS properly

## 🚀 Production Deployment

### Backend Deployment

1. **Choose hosting platform** (AWS, Heroku, DigitalOcean)
2. **Setup MongoDB Atlas** for cloud database
3. **Configure environment variables**
4. **Enable SSL/HTTPS**
5. **Setup monitoring and logging**

### Mobile App Deployment

#### Android

```bash
# Generate signed APK
cd android
./gradlew assembleRelease

# Upload to Google Play Store
```

#### iOS

```bash
# Archive for App Store
cd ios
xcodebuild -workspace HealthVisionAI.xcworkspace -scheme HealthVisionAI archive

# Upload to App Store Connect
```

## 🛠️ Development Tools

### Recommended VS Code Extensions

- React Native Tools
- ES7+ React/Redux/React-Native snippets
- Prettier - Code formatter
- ESLint
- MongoDB for VS Code

### Debugging

#### Backend Debugging

```bash
# Debug mode
npm run dev

# Use VS Code debugger or console.log
```

#### Mobile Debugging

```bash
# React Native Debugger
npm install -g react-native-debugger

# Flipper (Facebook's debugging platform)
# Download from https://fbflipper.com/
```

## 📊 Monitoring and Analytics

### Backend Monitoring

- **Health Check Endpoint**: `/health`
- **API Metrics**: Built-in request logging
- **Error Tracking**: Console and file logging

### Mobile Analytics

- **Crash Reporting**: React Native Crash Analytics
- **User Analytics**: Custom event tracking
- **Performance Monitoring**: Built-in performance metrics

## 🔧 Troubleshooting

### Common Issues

#### Backend Issues

```bash
# MongoDB connection error
# Check if MongoDB is running
# Verify connection string in .env

# Port already in use
# Change PORT in .env or kill process using port 5000
lsof -ti:5000 | xargs kill -9
```

#### Mobile Issues

```bash
# Metro bundler issues
npx react-native start --reset-cache

# Android build issues
cd android && ./gradlew clean && cd ..

# iOS build issues
cd ios && rm -rf build && cd ..
```

### Getting Help

- **Documentation**: Check `/docs` folder
- **Issues**: Create GitHub issue
- **Community**: Join our Discord server
- **Email**: <EMAIL>

## 🎯 Next Steps

1. **Complete the setup** following this guide
2. **Explore the codebase** and understand the architecture
3. **Run the sample data** and test all features
4. **Customize the app** for your specific needs
5. **Deploy to production** when ready

## 📈 Performance Optimization

### Backend Optimization

- Enable MongoDB indexing
- Implement caching with Redis
- Use compression middleware
- Optimize database queries

### Mobile Optimization

- Enable Hermes engine
- Optimize images and assets
- Implement lazy loading
- Use FlatList for large datasets

---

**🎉 Congratulations! You now have a fully functional HealthVision AI system running locally.**

For any questions or issues, please refer to the documentation or contact our support team.
