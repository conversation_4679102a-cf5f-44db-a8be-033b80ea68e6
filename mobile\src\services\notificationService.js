import PushNotification from 'react-native-push-notification';
import { Platform } from 'react-native';

class NotificationService {
  constructor() {
    this.configure();
  }

  configure() {
    PushNotification.configure({
      onRegister: function (token) {
        console.log('TOKEN:', token);
      },

      onNotification: function (notification) {
        console.log('NOTIFICATION:', notification);
      },

      onAction: function (notification) {
        console.log('ACTION:', notification.action);
        console.log('NOTIFICATION:', notification);
      },

      onRegistrationError: function(err) {
        console.error(err.message, err);
      },

      permissions: {
        alert: true,
        badge: true,
        sound: true,
      },

      popInitialNotification: true,
      requestPermissions: Platform.OS === 'ios',
    });
  }

  async initialize() {
    // Request permissions for notifications
    if (Platform.OS === 'android') {
      PushNotification.createChannel(
        {
          channelId: 'health-alerts',
          channelName: 'Health Alerts',
          channelDescription: 'Critical health notifications',
          importance: 4,
          vibrate: true,
        },
        (created) => console.log(`createChannel returned '${created}'`)
      );
    }
  }

  showLocalNotification(data) {
    PushNotification.localNotification({
      channelId: 'health-alerts',
      title: data.title || 'Health Alert',
      message: data.message || 'You have a new health notification',
      playSound: true,
      soundName: 'default',
      actions: ['View', 'Dismiss'],
    });
  }

  scheduleNotification(data, date) {
    PushNotification.localNotificationSchedule({
      channelId: 'health-alerts',
      title: data.title,
      message: data.message,
      date: date,
      playSound: true,
      soundName: 'default',
    });
  }

  cancelAllNotifications() {
    PushNotification.cancelAllLocalNotifications();
  }
}

export const notificationService = new NotificationService();
