# HealthVision AI - Automated Installation Script for Windows
# This script will set up the complete HealthVision AI project

Write-Host "🚀 HealthVision AI - Automated Installation Script" -ForegroundColor Cyan
Write-Host "=================================================" -ForegroundColor Cyan
Write-Host ""

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ This script requires administrator privileges!" -ForegroundColor Red
    Write-Host "Please run PowerShell as Administrator and try again." -ForegroundColor Yellow
    pause
    exit 1
}

# Function to check if a command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Function to install Chocolatey
function Install-Chocolatey {
    Write-Host "📦 Installing Chocolatey package manager..." -ForegroundColor Yellow
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
    refreshenv
}

# Function to install Node.js
function Install-NodeJS {
    Write-Host "📦 Installing Node.js..." -ForegroundColor Yellow
    choco install nodejs -y
    refreshenv
}

# Function to install MongoDB
function Install-MongoDB {
    Write-Host "📦 Installing MongoDB..." -ForegroundColor Yellow
    choco install mongodb -y
    
    # Start MongoDB service
    Write-Host "🔧 Starting MongoDB service..." -ForegroundColor Yellow
    net start MongoDB
}

# Function to install Git
function Install-Git {
    Write-Host "📦 Installing Git..." -ForegroundColor Yellow
    choco install git -y
    refreshenv
}

# Function to install Android Studio
function Install-AndroidStudio {
    Write-Host "📦 Installing Android Studio..." -ForegroundColor Yellow
    choco install androidstudio -y
    Write-Host "⚠️  Please complete Android Studio setup manually:" -ForegroundColor Yellow
    Write-Host "   1. Open Android Studio" -ForegroundColor White
    Write-Host "   2. Install Android SDK" -ForegroundColor White
    Write-Host "   3. Create a virtual device (AVD)" -ForegroundColor White
    Write-Host "   4. Accept all SDK licenses" -ForegroundColor White
}

# Function to setup backend
function Setup-Backend {
    Write-Host "🏗️  Setting up backend..." -ForegroundColor Green
    
    if (Test-Path "backend") {
        Set-Location backend
        
        Write-Host "📦 Installing backend dependencies..." -ForegroundColor Yellow
        npm install
        
        Write-Host "🔧 Setting up environment configuration..." -ForegroundColor Yellow
        if (!(Test-Path ".env")) {
            Copy-Item ".env.example" ".env"
            Write-Host "✅ Created .env file from template" -ForegroundColor Green
            Write-Host "⚠️  Please edit backend/.env with your configuration" -ForegroundColor Yellow
        }
        
        Write-Host "🌱 Seeding database with sample data..." -ForegroundColor Yellow
        npm run seed
        
        Set-Location ..
        Write-Host "✅ Backend setup completed!" -ForegroundColor Green
    } else {
        Write-Host "❌ Backend directory not found!" -ForegroundColor Red
    }
}

# Function to setup mobile app
function Setup-Mobile {
    Write-Host "📱 Setting up mobile app..." -ForegroundColor Green
    
    if (Test-Path "mobile") {
        Set-Location mobile
        
        Write-Host "📦 Installing mobile dependencies..." -ForegroundColor Yellow
        npm install
        
        Write-Host "🔧 Installing React Native CLI globally..." -ForegroundColor Yellow
        npm install -g react-native-cli
        
        Set-Location ..
        Write-Host "✅ Mobile app setup completed!" -ForegroundColor Green
    } else {
        Write-Host "❌ Mobile directory not found!" -ForegroundColor Red
    }
}

# Function to create startup scripts
function Create-StartupScripts {
    Write-Host "📝 Creating startup scripts..." -ForegroundColor Yellow
    
    # Backend startup script
    @"
@echo off
echo 🚀 Starting HealthVision AI Backend...
cd backend
npm run dev
pause
"@ | Out-File -FilePath "start-backend.bat" -Encoding ASCII
    
    # Mobile startup script
    @"
@echo off
echo 📱 Starting HealthVision AI Mobile App...
cd mobile
start cmd /k "npm start"
timeout /t 5
npm run android
pause
"@ | Out-File -FilePath "start-mobile.bat" -Encoding ASCII
    
    # Combined startup script
    @"
@echo off
echo 🚀 Starting HealthVision AI Complete System...
echo.
echo Starting Backend Server...
start cmd /k "cd backend && npm run dev"
timeout /t 5
echo.
echo Starting Mobile App...
start cmd /k "cd mobile && npm start"
timeout /t 10
cd mobile
npm run android
pause
"@ | Out-File -FilePath "start-all.bat" -Encoding ASCII
    
    Write-Host "✅ Startup scripts created!" -ForegroundColor Green
    Write-Host "   - start-backend.bat: Start backend only" -ForegroundColor White
    Write-Host "   - start-mobile.bat: Start mobile app only" -ForegroundColor White
    Write-Host "   - start-all.bat: Start complete system" -ForegroundColor White
}

# Main installation process
try {
    Write-Host "🔍 Checking system requirements..." -ForegroundColor Yellow
    
    # Check and install Chocolatey
    if (!(Test-Command "choco")) {
        Install-Chocolatey
    } else {
        Write-Host "✅ Chocolatey already installed" -ForegroundColor Green
    }
    
    # Check and install Node.js
    if (!(Test-Command "node")) {
        Install-NodeJS
    } else {
        $nodeVersion = node --version
        Write-Host "✅ Node.js already installed: $nodeVersion" -ForegroundColor Green
    }
    
    # Check and install Git
    if (!(Test-Command "git")) {
        Install-Git
    } else {
        $gitVersion = git --version
        Write-Host "✅ Git already installed: $gitVersion" -ForegroundColor Green
    }
    
    # Check and install MongoDB
    $mongoService = Get-Service -Name "MongoDB" -ErrorAction SilentlyContinue
    if (!$mongoService) {
        Install-MongoDB
    } else {
        Write-Host "✅ MongoDB already installed" -ForegroundColor Green
        if ($mongoService.Status -ne "Running") {
            Write-Host "🔧 Starting MongoDB service..." -ForegroundColor Yellow
            Start-Service -Name "MongoDB"
        }
    }
    
    # Ask about Android Studio
    $installAndroid = Read-Host "Do you want to install Android Studio for mobile development? (y/n)"
    if ($installAndroid -eq "y" -or $installAndroid -eq "Y") {
        if (!(Test-Path "C:\Program Files\Android\Android Studio")) {
            Install-AndroidStudio
        } else {
            Write-Host "✅ Android Studio already installed" -ForegroundColor Green
        }
    }
    
    Write-Host ""
    Write-Host "🏗️  Setting up HealthVision AI project..." -ForegroundColor Cyan
    
    # Setup backend
    Setup-Backend
    
    # Setup mobile app
    Setup-Mobile
    
    # Create startup scripts
    Create-StartupScripts
    
    Write-Host ""
    Write-Host "🎉 Installation completed successfully!" -ForegroundColor Green
    Write-Host "=================================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "📋 Next Steps:" -ForegroundColor Yellow
    Write-Host "1. Edit backend/.env with your configuration" -ForegroundColor White
    Write-Host "2. If you installed Android Studio, complete its setup" -ForegroundColor White
    Write-Host "3. Run 'start-all.bat' to start the complete system" -ForegroundColor White
    Write-Host "4. Access the API documentation at http://localhost:5000/api-docs" -ForegroundColor White
    Write-Host ""
    Write-Host "🚀 Quick Start Commands:" -ForegroundColor Yellow
    Write-Host "   Backend only: start-backend.bat" -ForegroundColor White
    Write-Host "   Mobile only:  start-mobile.bat" -ForegroundColor White
    Write-Host "   Full system:  start-all.bat" -ForegroundColor White
    Write-Host ""
    Write-Host "📚 Documentation: See setup.md for detailed instructions" -ForegroundColor Yellow
    Write-Host "🆘 Support: Check README.md for troubleshooting" -ForegroundColor Yellow
    Write-Host ""
    
    $startNow = Read-Host "Do you want to start the system now? (y/n)"
    if ($startNow -eq "y" -or $startNow -eq "Y") {
        Write-Host "🚀 Starting HealthVision AI..." -ForegroundColor Green
        Start-Process -FilePath "start-all.bat"
    }
    
} catch {
    Write-Host "❌ Installation failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please check the error and try again, or install manually using setup.md" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
