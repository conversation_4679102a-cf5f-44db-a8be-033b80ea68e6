import React from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
} from 'react-native';
import {
  Text,
  Card,
  List,
  IconButton,
} from 'react-native-paper';
import * as Animatable from 'react-native-animatable';

import { useTheme } from '../contexts/ThemeContext';

const SettingsScreen = ({ navigation }) => {
  const { theme } = useTheme();

  const renderHeader = () => (
    <Animatable.View animation="fadeInDown" duration={800}>
      <View style={[styles.header, { backgroundColor: theme.colors.primary }]}>
        <IconButton
          icon="arrow-left"
          size={24}
          iconColor={theme.colors.white}
          onPress={() => navigation.goBack()}
        />
        <Text style={[styles.headerTitle, { color: theme.colors.white }]}>
          Settings
        </Text>
        <View style={{ width: 48 }} />
      </View>
    </Animatable.View>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {renderHeader()}
      
      <ScrollView style={styles.content}>
        <Animatable.View animation="fadeInUp" duration={800} delay={200}>
          <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
            <Card.Content>
              <List.Section>
                <List.Item
                  title="General Settings"
                  description="App preferences and configuration"
                  left={(props) => <List.Icon {...props} icon="cog" />}
                  titleStyle={{ color: theme.colors.text }}
                  descriptionStyle={{ color: theme.colors.text }}
                />
              </List.Section>
            </Card.Content>
          </Card>
        </Animatable.View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 4,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  card: {
    borderRadius: 16,
    elevation: 4,
  },
});

export default SettingsScreen;
