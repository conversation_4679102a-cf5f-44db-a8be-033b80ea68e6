const express = require('express');
const { query, validationResult } = require('express-validator');
const HealthPrediction = require('../models/HealthPrediction');
const router = express.Router();

/**
 * @swagger
 * /api/predictions:
 *   get:
 *     summary: Get user's health predictions
 *     tags: [Health Predictions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: prediction_type
 *         schema:
 *           type: string
 *         description: Filter by prediction type
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of predictions to return
 *     responses:
 *       200:
 *         description: Predictions retrieved successfully
 */
router.get('/', [
  query('prediction_type').optional().isIn([
    'disease_risk', 'wellness_score', 'lifestyle_recommendation',
    'medication_adherence', 'health_trend', 'emergency_prediction',
    'fitness_goal_achievement', 'sleep_quality_prediction',
    'stress_level_forecast', 'nutrition_recommendation'
  ]),
  query('limit').optional().isInt({ min: 1, max: 100 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const { prediction_type, limit = 10 } = req.query;

    // Build query
    const query = { 
      user_id: req.user.userId,
      status: 'active'
    };

    if (prediction_type) {
      query.prediction_type = prediction_type;
    }

    const predictions = await HealthPrediction.find(query)
      .sort({ prediction_date: -1 })
      .limit(parseInt(limit));

    // Get summary statistics
    const summary = await HealthPrediction.aggregate([
      { $match: { user_id: req.user.userId, status: 'active' } },
      {
        $group: {
          _id: '$prediction_type',
          count: { $sum: 1 },
          avg_risk_score: { $avg: '$risk_score' },
          avg_confidence: { $avg: '$confidence_level' }
        }
      }
    ]);

    res.json({
      success: true,
      message: 'Predictions retrieved successfully',
      data: {
        predictions,
        summary,
        total_count: predictions.length
      }
    });

  } catch (error) {
    console.error('Get predictions error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

/**
 * @swagger
 * /api/predictions/latest:
 *   get:
 *     summary: Get latest predictions for each type
 *     tags: [Health Predictions]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Latest predictions retrieved successfully
 */
router.get('/latest', async (req, res) => {
  try {
    const latestPredictions = await HealthPrediction.getLatestPredictions(req.user.userId);

    res.json({
      success: true,
      message: 'Latest predictions retrieved successfully',
      data: latestPredictions
    });

  } catch (error) {
    console.error('Get latest predictions error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

/**
 * @swagger
 * /api/predictions/high-risk:
 *   get:
 *     summary: Get high-risk predictions
 *     tags: [Health Predictions]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: High-risk predictions retrieved successfully
 */
router.get('/high-risk', async (req, res) => {
  try {
    const highRiskPredictions = await HealthPrediction.getHighRiskPredictions(req.user.userId);

    res.json({
      success: true,
      message: 'High-risk predictions retrieved successfully',
      data: highRiskPredictions
    });

  } catch (error) {
    console.error('Get high-risk predictions error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

/**
 * @swagger
 * /api/predictions/consultation-required:
 *   get:
 *     summary: Get predictions requiring medical consultation
 *     tags: [Health Predictions]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Consultation-required predictions retrieved successfully
 */
router.get('/consultation-required', async (req, res) => {
  try {
    const consultationPredictions = await HealthPrediction.getPredictionsRequiringConsultation(req.user.userId);

    res.json({
      success: true,
      message: 'Consultation-required predictions retrieved successfully',
      data: consultationPredictions
    });

  } catch (error) {
    console.error('Get consultation predictions error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

/**
 * @swagger
 * /api/predictions/{id}:
 *   get:
 *     summary: Get specific prediction details
 *     tags: [Health Predictions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Prediction ID
 *     responses:
 *       200:
 *         description: Prediction details retrieved successfully
 */
router.get('/:id', async (req, res) => {
  try {
    const prediction = await HealthPrediction.findOne({
      prediction_id: req.params.id,
      user_id: req.user.userId
    });

    if (!prediction) {
      return res.status(404).json({
        success: false,
        message: 'Prediction not found'
      });
    }

    res.json({
      success: true,
      message: 'Prediction details retrieved successfully',
      data: prediction
    });

  } catch (error) {
    console.error('Get prediction details error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

/**
 * @swagger
 * /api/predictions/analytics/trends:
 *   get:
 *     summary: Get prediction trends and analytics
 *     tags: [Health Predictions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [week, month, quarter, year]
 *           default: month
 *     responses:
 *       200:
 *         description: Prediction trends retrieved successfully
 */
router.get('/analytics/trends', [
  query('period').optional().isIn(['week', 'month', 'quarter', 'year'])
], async (req, res) => {
  try {
    const { period = 'month' } = req.query;

    // Calculate date range
    const now = new Date();
    const periodDays = { week: 7, month: 30, quarter: 90, year: 365 };
    const startDate = new Date(now.getTime() - (periodDays[period] * 24 * 60 * 60 * 1000));

    // Get trend data
    const trendData = await HealthPrediction.aggregate([
      {
        $match: {
          user_id: req.user.userId,
          prediction_date: { $gte: startDate, $lte: now },
          status: 'active'
        }
      },
      {
        $group: {
          _id: {
            type: '$prediction_type',
            date: {
              $dateToString: {
                format: period === 'week' ? '%Y-%m-%d' : '%Y-%m',
                date: '$prediction_date'
              }
            }
          },
          avg_risk_score: { $avg: '$risk_score' },
          avg_confidence: { $avg: '$confidence_level' },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.date': 1 } }
    ]);

    // Get risk distribution
    const riskDistribution = await HealthPrediction.aggregate([
      {
        $match: {
          user_id: req.user.userId,
          prediction_date: { $gte: startDate, $lte: now },
          status: 'active'
        }
      },
      {
        $bucket: {
          groupBy: '$risk_score',
          boundaries: [0, 25, 50, 75, 100],
          default: 'Other',
          output: {
            count: { $sum: 1 },
            avg_confidence: { $avg: '$confidence_level' }
          }
        }
      }
    ]);

    res.json({
      success: true,
      message: 'Prediction trends retrieved successfully',
      data: {
        period,
        trend_data: trendData,
        risk_distribution: riskDistribution
      }
    });

  } catch (error) {
    console.error('Get prediction trends error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

module.exports = router;
