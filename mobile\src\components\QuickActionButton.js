import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Text } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import LinearGradient from 'react-native-linear-gradient';
import * as Animatable from 'react-native-animatable';
import { useTheme } from '../contexts/ThemeContext';

const QuickActionButton = ({ icon, label, onPress, color, style }) => {
  const { theme } = useTheme();
  const buttonColor = color || theme.colors.primary;

  return (
    <Animatable.View animation="bounceIn" duration={600}>
      <TouchableOpacity onPress={onPress} activeOpacity={0.8} style={style}>
        <View style={[styles.container, { backgroundColor: theme.colors.surface }]}>
          <LinearGradient
            colors={[`${buttonColor}20`, `${buttonColor}10`]}
            style={styles.gradient}
          >
            <View style={[styles.iconContainer, { backgroundColor: `${buttonColor}20` }]}>
              <Icon name={icon} size={28} color={buttonColor} />
            </View>
            <Text style={[styles.label, { color: theme.colors.text }]}>
              {label}
            </Text>
          </LinearGradient>
        </View>
      </TouchableOpacity>
    </Animatable.View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '48%',
    aspectRatio: 1,
    borderRadius: 16,
    elevation: 4,
    overflow: 'hidden',
    marginBottom: 16,
  },
  gradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  iconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
    lineHeight: 18,
  },
});

export default QuickActionButton;
