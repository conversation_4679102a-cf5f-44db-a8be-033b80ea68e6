import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  RefreshControl,
} from 'react-native';
import {
  Text,
  Card,
  Searchbar,
  SegmentedButtons,
  Chip,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import * as Animatable from 'react-native-animatable';

import { useTheme } from '../contexts/ThemeContext';
import { predictionService } from '../services/predictionService';
import PredictionCard from '../components/PredictionCard';

const PredictionsScreen = ({ navigation }) => {
  const { theme } = useTheme();
  
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [predictions, setPredictions] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadPredictions();
  }, [selectedFilter]);

  const loadPredictions = async () => {
    try {
      setLoading(true);
      let response;
      
      switch (selectedFilter) {
        case 'high_risk':
          response = await predictionService.getHighRiskPredictions();
          break;
        case 'recent':
          response = await predictionService.getLatestPredictions(20);
          break;
        default:
          response = await predictionService.getPredictions();
      }
      
      if (response.success) {
        setPredictions(response.data.predictions || response.data);
      }
    } catch (error) {
      console.error('Error loading predictions:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadPredictions();
    setRefreshing(false);
  };

  const filteredPredictions = predictions.filter(prediction =>
    prediction.condition_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    prediction.prediction_type.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const renderHeader = () => (
    <Animatable.View animation="fadeInDown" duration={800}>
      <View style={[styles.header, { backgroundColor: theme.colors.primary }]}>
        <View style={styles.headerContent}>
          <Icon name="brain" size={32} color={theme.colors.white} />
          <View style={styles.headerText}>
            <Text style={[styles.headerTitle, { color: theme.colors.white }]}>
              AI Health Insights
            </Text>
            <Text style={[styles.headerSubtitle, { color: theme.colors.white }]}>
              Personalized health predictions
            </Text>
          </View>
        </View>
      </View>
    </Animatable.View>
  );

  const renderSearchAndFilters = () => (
    <Animatable.View animation="fadeInUp" duration={800} delay={200}>
      <View style={styles.searchContainer}>
        <Searchbar
          placeholder="Search predictions..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={[styles.searchbar, { backgroundColor: theme.colors.surface }]}
          iconColor={theme.colors.primary}
        />
        
        <SegmentedButtons
          value={selectedFilter}
          onValueChange={setSelectedFilter}
          buttons={[
            { value: 'all', label: 'All' },
            { value: 'high_risk', label: 'High Risk' },
            { value: 'recent', label: 'Recent' },
          ]}
          style={styles.filterButtons}
        />
      </View>
    </Animatable.View>
  );

  const renderSummaryCards = () => {
    const highRiskCount = predictions.filter(p => p.risk_score >= 60).length;
    const totalPredictions = predictions.length;
    const avgConfidence = predictions.length > 0 
      ? Math.round(predictions.reduce((sum, p) => sum + p.confidence_level, 0) / predictions.length)
      : 0;

    return (
      <Animatable.View animation="fadeInUp" duration={800} delay={400}>
        <View style={styles.summaryContainer}>
          <Card style={[styles.summaryCard, { backgroundColor: theme.colors.surface }]}>
            <Card.Content style={styles.summaryContent}>
              <Icon name="alert-circle" size={24} color={theme.colors.warning} />
              <View style={styles.summaryText}>
                <Text style={[styles.summaryValue, { color: theme.colors.text }]}>
                  {highRiskCount}
                </Text>
                <Text style={[styles.summaryLabel, { color: theme.colors.text }]}>
                  High Risk
                </Text>
              </View>
            </Card.Content>
          </Card>

          <Card style={[styles.summaryCard, { backgroundColor: theme.colors.surface }]}>
            <Card.Content style={styles.summaryContent}>
              <Icon name="chart-line" size={24} color={theme.colors.info} />
              <View style={styles.summaryText}>
                <Text style={[styles.summaryValue, { color: theme.colors.text }]}>
                  {totalPredictions}
                </Text>
                <Text style={[styles.summaryLabel, { color: theme.colors.text }]}>
                  Total Insights
                </Text>
              </View>
            </Card.Content>
          </Card>

          <Card style={[styles.summaryCard, { backgroundColor: theme.colors.surface }]}>
            <Card.Content style={styles.summaryContent}>
              <Icon name="check-circle" size={24} color={theme.colors.success} />
              <View style={styles.summaryText}>
                <Text style={[styles.summaryValue, { color: theme.colors.text }]}>
                  {avgConfidence}%
                </Text>
                <Text style={[styles.summaryLabel, { color: theme.colors.text }]}>
                  Avg Confidence
                </Text>
              </View>
            </Card.Content>
          </Card>
        </View>
      </Animatable.View>
    );
  };

  const renderPredictionsList = () => (
    <Animatable.View animation="fadeInUp" duration={800} delay={600}>
      <View style={styles.predictionsContainer}>
        <View style={styles.sectionHeader}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Your Health Predictions
          </Text>
          <Chip
            mode="flat"
            style={[styles.countChip, { backgroundColor: `${theme.colors.primary}20` }]}
            textStyle={{ color: theme.colors.primary }}
          >
            {filteredPredictions.length}
          </Chip>
        </View>

        {filteredPredictions.length > 0 ? (
          filteredPredictions.map((prediction, index) => (
            <PredictionCard
              key={prediction.prediction_id || index}
              prediction={prediction}
              onPress={() => navigation.navigate('PredictionDetails', { prediction })}
            />
          ))
        ) : (
          <Card style={[styles.emptyCard, { backgroundColor: theme.colors.surface }]}>
            <Card.Content style={styles.emptyContent}>
              <Icon name="brain" size={64} color={theme.colors.primary} />
              <Text style={[styles.emptyTitle, { color: theme.colors.text }]}>
                No Predictions Found
              </Text>
              <Text style={[styles.emptyMessage, { color: theme.colors.text }]}>
                {searchQuery 
                  ? 'Try adjusting your search terms'
                  : 'Add some health data to get AI-powered insights'
                }
              </Text>
            </Card.Content>
          </Card>
        )}
      </View>
    </Animatable.View>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {renderHeader()}
      
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderSearchAndFilters()}
        {renderSummaryCards()}
        {renderPredictionsList()}
        
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerText: {
    marginLeft: 16,
    flex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    opacity: 0.9,
  },
  content: {
    flex: 1,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  searchbar: {
    elevation: 2,
    marginBottom: 16,
  },
  filterButtons: {
    marginBottom: 8,
  },
  summaryContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  summaryCard: {
    flex: 1,
    marginHorizontal: 4,
    borderRadius: 12,
    elevation: 2,
  },
  summaryContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  summaryText: {
    marginLeft: 12,
    alignItems: 'center',
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  summaryLabel: {
    fontSize: 12,
    opacity: 0.7,
  },
  predictionsContainer: {
    paddingHorizontal: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  countChip: {
    height: 28,
  },
  emptyCard: {
    borderRadius: 16,
    elevation: 2,
  },
  emptyContent: {
    alignItems: 'center',
    padding: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyMessage: {
    fontSize: 14,
    textAlign: 'center',
    opacity: 0.7,
    lineHeight: 20,
  },
  bottomSpacing: {
    height: 100,
  },
});

export default PredictionsScreen;
