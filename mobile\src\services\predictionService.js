import { authService } from './authService';

class PredictionService {
  async getLatestPredictions(limit = 10) {
    return authService.makeRequest(`/predictions/latest?limit=${limit}`);
  }

  async getPredictions(filters = {}) {
    const queryParams = new URLSearchParams(filters).toString();
    return authService.makeRequest(`/predictions?${queryParams}`);
  }

  async getHighRiskPredictions() {
    return authService.makeRequest('/predictions/high-risk');
  }

  async getPredictionDetails(predictionId) {
    return authService.makeRequest(`/predictions/${predictionId}`);
  }

  async getPredictionTrends(period = 'month') {
    return authService.makeRequest(`/predictions/analytics/trends?period=${period}`);
  }
}

export const predictionService = new PredictionService();
