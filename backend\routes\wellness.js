const express = require('express');
const router = express.Router();

// GET /api/wellness/plans - Get user's wellness plans
router.get('/plans', async (req, res) => {
  try {
    const plans = [
      {
        plan_id: 'plan_1',
        plan_name: 'Weight Loss Program',
        plan_type: 'diet',
        status: 'active',
        progress_percentage: 65,
        start_date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        end_date: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000)
      }
    ];

    res.json({
      success: true,
      message: 'Wellness plans retrieved successfully',
      data: plans
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

module.exports = router;
