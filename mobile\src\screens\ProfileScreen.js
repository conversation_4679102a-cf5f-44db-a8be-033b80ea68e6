import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  Avatar,
  List,
  Switch,
  Button,
  Divider,
} from 'react-native-paper';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import * as Animatable from 'react-native-animatable';

import { useTheme } from '../contexts/ThemeContext';
import { useAuth } from '../contexts/AuthContext';

const ProfileScreen = ({ navigation }) => {
  const { theme, changeTheme, userTheme } = useTheme();
  const { user, logout } = useAuth();
  
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [biometricAuthEnabled, setBiometricAuthEnabled] = useState(false);

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Logout', 
          style: 'destructive',
          onPress: logout 
        },
      ]
    );
  };

  const handleThemeChange = (newTheme) => {
    changeTheme(newTheme);
  };

  const renderProfileHeader = () => (
    <Animatable.View animation="fadeInDown" duration={800}>
      <LinearGradient
        colors={[theme.colors.gradientStart, theme.colors.gradientEnd]}
        style={styles.headerGradient}
      >
        <View style={styles.profileHeader}>
          <Avatar.Image
            size={80}
            source={{ uri: user?.profile_picture || 'https://via.placeholder.com/80' }}
            style={styles.avatar}
          />
          <View style={styles.profileInfo}>
            <Text style={[styles.userName, { color: theme.colors.white }]}>
              {user?.full_name || `${user?.first_name} ${user?.last_name}`}
            </Text>
            <Text style={[styles.userEmail, { color: theme.colors.white }]}>
              {user?.email}
            </Text>
            <View style={styles.subscriptionBadge}>
              <Text style={[styles.subscriptionText, { color: theme.colors.white }]}>
                {user?.subscription_type?.toUpperCase() || 'FREE'} PLAN
              </Text>
            </View>
          </View>
        </View>
      </LinearGradient>
    </Animatable.View>
  );

  const renderStatsCards = () => (
    <Animatable.View animation="fadeInUp" duration={800} delay={200}>
      <View style={styles.statsContainer}>
        <Card style={[styles.statCard, { backgroundColor: theme.colors.surface }]}>
          <Card.Content style={styles.statContent}>
            <Icon name="heart-pulse" size={24} color={theme.colors.heartRate} />
            <Text style={[styles.statValue, { color: theme.colors.text }]}>
              1,247
            </Text>
            <Text style={[styles.statLabel, { color: theme.colors.text }]}>
              Health Records
            </Text>
          </Card.Content>
        </Card>

        <Card style={[styles.statCard, { backgroundColor: theme.colors.surface }]}>
          <Card.Content style={styles.statContent}>
            <Icon name="trophy" size={24} color={theme.colors.warning} />
            <Text style={[styles.statValue, { color: theme.colors.text }]}>
              23
            </Text>
            <Text style={[styles.statLabel, { color: theme.colors.text }]}>
              Achievements
            </Text>
          </Card.Content>
        </Card>

        <Card style={[styles.statCard, { backgroundColor: theme.colors.surface }]}>
          <Card.Content style={styles.statContent}>
            <Icon name="calendar-check" size={24} color={theme.colors.success} />
            <Text style={[styles.statValue, { color: theme.colors.text }]}>
              156
            </Text>
            <Text style={[styles.statLabel, { color: theme.colors.text }]}>
              Days Active
            </Text>
          </Card.Content>
        </Card>
      </View>
    </Animatable.View>
  );

  const renderMenuSection = () => (
    <Animatable.View animation="fadeInUp" duration={800} delay={400}>
      <Card style={[styles.menuCard, { backgroundColor: theme.colors.surface }]}>
        <Card.Content style={styles.menuContent}>
          <List.Section>
            <List.Subheader style={{ color: theme.colors.text }}>
              Account
            </List.Subheader>
            
            <List.Item
              title="Edit Profile"
              description="Update your personal information"
              left={(props) => <List.Icon {...props} icon="account-edit" color={theme.colors.primary} />}
              right={(props) => <List.Icon {...props} icon="chevron-right" color={theme.colors.text} />}
              onPress={() => navigation.navigate('EditProfile')}
              titleStyle={{ color: theme.colors.text }}
              descriptionStyle={{ color: theme.colors.text }}
            />
            
            <List.Item
              title="Health Profile"
              description="Medical history and conditions"
              left={(props) => <List.Icon {...props} icon="medical-bag" color={theme.colors.primary} />}
              right={(props) => <List.Icon {...props} icon="chevron-right" color={theme.colors.text} />}
              onPress={() => navigation.navigate('HealthProfile')}
              titleStyle={{ color: theme.colors.text }}
              descriptionStyle={{ color: theme.colors.text }}
            />
            
            <List.Item
              title="Emergency Contacts"
              description="Manage emergency contacts"
              left={(props) => <List.Icon {...props} icon="phone-alert" color={theme.colors.primary} />}
              right={(props) => <List.Icon {...props} icon="chevron-right" color={theme.colors.text} />}
              onPress={() => navigation.navigate('EmergencyContacts')}
              titleStyle={{ color: theme.colors.text }}
              descriptionStyle={{ color: theme.colors.text }}
            />

            <Divider style={styles.divider} />

            <List.Subheader style={{ color: theme.colors.text }}>
              Preferences
            </List.Subheader>

            <List.Item
              title="Theme"
              description={`Current: ${userTheme === 'auto' ? 'Auto' : userTheme === 'dark' ? 'Dark' : 'Light'}`}
              left={(props) => <List.Icon {...props} icon="palette" color={theme.colors.primary} />}
              right={() => (
                <View style={styles.themeButtons}>
                  <TouchableOpacity
                    onPress={() => handleThemeChange('light')}
                    style={[
                      styles.themeButton,
                      { backgroundColor: userTheme === 'light' ? theme.colors.primary : 'transparent' }
                    ]}
                  >
                    <Icon 
                      name="white-balance-sunny" 
                      size={16} 
                      color={userTheme === 'light' ? theme.colors.white : theme.colors.text} 
                    />
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={() => handleThemeChange('dark')}
                    style={[
                      styles.themeButton,
                      { backgroundColor: userTheme === 'dark' ? theme.colors.primary : 'transparent' }
                    ]}
                  >
                    <Icon 
                      name="moon-waning-crescent" 
                      size={16} 
                      color={userTheme === 'dark' ? theme.colors.white : theme.colors.text} 
                    />
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={() => handleThemeChange('auto')}
                    style={[
                      styles.themeButton,
                      { backgroundColor: userTheme === 'auto' ? theme.colors.primary : 'transparent' }
                    ]}
                  >
                    <Icon 
                      name="brightness-auto" 
                      size={16} 
                      color={userTheme === 'auto' ? theme.colors.white : theme.colors.text} 
                    />
                  </TouchableOpacity>
                </View>
              )}
              titleStyle={{ color: theme.colors.text }}
              descriptionStyle={{ color: theme.colors.text }}
            />

            <List.Item
              title="Notifications"
              description="Push notifications and alerts"
              left={(props) => <List.Icon {...props} icon="bell" color={theme.colors.primary} />}
              right={() => (
                <Switch
                  value={notificationsEnabled}
                  onValueChange={setNotificationsEnabled}
                  color={theme.colors.primary}
                />
              )}
              titleStyle={{ color: theme.colors.text }}
              descriptionStyle={{ color: theme.colors.text }}
            />

            <List.Item
              title="Biometric Authentication"
              description="Use fingerprint or face ID"
              left={(props) => <List.Icon {...props} icon="fingerprint" color={theme.colors.primary} />}
              right={() => (
                <Switch
                  value={biometricAuthEnabled}
                  onValueChange={setBiometricAuthEnabled}
                  color={theme.colors.primary}
                />
              )}
              titleStyle={{ color: theme.colors.text }}
              descriptionStyle={{ color: theme.colors.text }}
            />

            <Divider style={styles.divider} />

            <List.Subheader style={{ color: theme.colors.text }}>
              Support
            </List.Subheader>

            <List.Item
              title="Help & Support"
              description="Get help and contact support"
              left={(props) => <List.Icon {...props} icon="help-circle" color={theme.colors.primary} />}
              right={(props) => <List.Icon {...props} icon="chevron-right" color={theme.colors.text} />}
              onPress={() => navigation.navigate('Support')}
              titleStyle={{ color: theme.colors.text }}
              descriptionStyle={{ color: theme.colors.text }}
            />

            <List.Item
              title="Privacy Policy"
              description="Read our privacy policy"
              left={(props) => <List.Icon {...props} icon="shield-check" color={theme.colors.primary} />}
              right={(props) => <List.Icon {...props} icon="chevron-right" color={theme.colors.text} />}
              onPress={() => navigation.navigate('PrivacyPolicy')}
              titleStyle={{ color: theme.colors.text }}
              descriptionStyle={{ color: theme.colors.text }}
            />

            <List.Item
              title="About"
              description="App version and information"
              left={(props) => <List.Icon {...props} icon="information" color={theme.colors.primary} />}
              right={(props) => <List.Icon {...props} icon="chevron-right" color={theme.colors.text} />}
              onPress={() => navigation.navigate('About')}
              titleStyle={{ color: theme.colors.text }}
              descriptionStyle={{ color: theme.colors.text }}
            />
          </List.Section>
        </Card.Content>
      </Card>
    </Animatable.View>
  );

  const renderLogoutButton = () => (
    <Animatable.View animation="fadeInUp" duration={800} delay={600}>
      <View style={styles.logoutContainer}>
        <Button
          mode="outlined"
          onPress={handleLogout}
          icon="logout"
          style={[styles.logoutButton, { borderColor: theme.colors.error }]}
          labelStyle={{ color: theme.colors.error }}
        >
          Logout
        </Button>
      </View>
    </Animatable.View>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderProfileHeader()}
        {renderStatsCards()}
        {renderMenuSection()}
        {renderLogoutButton()}
        
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  headerGradient: {
    paddingTop: 50,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    marginRight: 20,
  },
  profileInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 16,
    opacity: 0.9,
    marginBottom: 8,
  },
  subscriptionBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 12,
    paddingVertical: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 12,
  },
  subscriptionText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginTop: -20,
    marginBottom: 20,
  },
  statCard: {
    flex: 1,
    marginHorizontal: 4,
    borderRadius: 12,
    elevation: 4,
  },
  statContent: {
    alignItems: 'center',
    padding: 16,
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 8,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    opacity: 0.7,
    textAlign: 'center',
  },
  menuCard: {
    marginHorizontal: 20,
    borderRadius: 16,
    elevation: 4,
  },
  menuContent: {
    padding: 0,
  },
  divider: {
    marginVertical: 8,
  },
  themeButtons: {
    flexDirection: 'row',
  },
  themeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 4,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
  },
  logoutContainer: {
    paddingHorizontal: 20,
    marginTop: 20,
  },
  logoutButton: {
    paddingVertical: 8,
    borderRadius: 12,
  },
  bottomSpacing: {
    height: 100,
  },
});

export default ProfileScreen;
