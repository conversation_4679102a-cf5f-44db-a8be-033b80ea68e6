import React, { createContext, useContext, useState, useEffect } from 'react';
import { biometricService } from '../services/biometricService';

const HealthDataContext = createContext({});

export const useHealthData = () => {
  const context = useContext(HealthDataContext);
  if (!context) {
    throw new Error('useHealthData must be used within a HealthDataProvider');
  }
  return context;
};

export const HealthDataProvider = ({ children, socket }) => {
  const [latestReadings, setLatestReadings] = useState({});
  const [emergencyAlerts, setEmergencyAlerts] = useState([]);
  const [healthScore, setHealthScore] = useState(85);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadLatestReadings();
    
    if (socket) {
      socket.on('biometric_update', handleBiometricUpdate);
      socket.on('emergency_alert', handleEmergencyAlert);
      
      return () => {
        socket.off('biometric_update', handleBiometricUpdate);
        socket.off('emergency_alert', handleEmergencyAlert);
      };
    }
  }, [socket]);

  const loadLatestReadings = async () => {
    try {
      setLoading(true);
      const response = await biometricService.getLatestReadings();
      
      if (response.success) {
        const readingsMap = {};
        response.data.forEach(reading => {
          readingsMap[reading.measurement_type] = reading;
        });
        setLatestReadings(readingsMap);
      }
    } catch (error) {
      console.error('Error loading latest readings:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleBiometricUpdate = (data) => {
    if (data.type === 'new_reading') {
      setLatestReadings(prev => ({
        ...prev,
        [data.data.measurement_type]: data.data
      }));
    }
  };

  const handleEmergencyAlert = (alert) => {
    setEmergencyAlerts(prev => [alert, ...prev.slice(0, 4)]); // Keep only 5 alerts
  };

  const addBiometricReading = async (readingData) => {
    try {
      setLoading(true);
      const response = await biometricService.addReading(readingData);
      
      if (response.success) {
        setLatestReadings(prev => ({
          ...prev,
          [response.data.measurement_type]: response.data
        }));
        return { success: true };
      } else {
        return { success: false, message: response.message };
      }
    } catch (error) {
      console.error('Error adding biometric reading:', error);
      return { success: false, message: 'Failed to add reading. Please try again.' };
    } finally {
      setLoading(false);
    }
  };

  const dismissEmergencyAlert = (alertId) => {
    setEmergencyAlerts(prev => prev.filter(alert => alert.id !== alertId));
  };

  const value = {
    latestReadings,
    emergencyAlerts,
    healthScore,
    loading,
    addBiometricReading,
    dismissEmergencyAlert,
    refreshData: loadLatestReadings
  };

  return (
    <HealthDataContext.Provider value={value}>
      {children}
    </HealthDataContext.Provider>
  );
};
