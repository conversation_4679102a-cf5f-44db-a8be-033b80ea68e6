import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Card,
  IconButton,
} from 'react-native-paper';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { showMessage } from 'react-native-flash-message';
import * as Animatable from 'react-native-animatable';

import { useTheme } from '../../contexts/ThemeContext';
import { authService } from '../../services/authService';

const ForgotPasswordScreen = ({ navigation }) => {
  const { theme } = useTheme();
  
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);

  const handleForgotPassword = async () => {
    if (!email) {
      showMessage({
        message: 'Please enter your email address',
        type: 'warning',
      });
      return;
    }

    try {
      setLoading(true);
      const result = await authService.forgotPassword(email);
      
      if (result.success) {
        setEmailSent(true);
        showMessage({
          message: 'Password reset link sent to your email',
          type: 'success',
        });
      } else {
        showMessage({
          message: result.message || 'Failed to send reset email',
          type: 'danger',
        });
      }
    } catch (error) {
      showMessage({
        message: 'An error occurred. Please try again.',
        type: 'danger',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <LinearGradient
        colors={[theme.colors.gradientStart, theme.colors.gradientEnd]}
        style={styles.header}
      >
        <IconButton
          icon="arrow-left"
          size={24}
          iconColor={theme.colors.white}
          onPress={() => navigation.goBack()}
          style={styles.backButton}
        />
        
        <Animatable.View animation="fadeInDown" duration={1000}>
          <Icon name="lock-reset" size={80} color={theme.colors.white} />
          <Text style={[styles.title, { color: theme.colors.white }]}>
            Reset Password
          </Text>
          <Text style={[styles.subtitle, { color: theme.colors.white }]}>
            Enter your email to receive reset instructions
          </Text>
        </Animatable.View>
      </LinearGradient>

      <View style={styles.content}>
        <Animatable.View animation="fadeInUp" duration={1000} delay={300}>
          <Card style={[styles.resetCard, { backgroundColor: theme.colors.surface }]}>
            <Card.Content style={styles.cardContent}>
              {!emailSent ? (
                <>
                  <Text style={[styles.instruction, { color: theme.colors.text }]}>
                    Enter the email address associated with your account and we'll send you a link to reset your password.
                  </Text>

                  <TextInput
                    label="Email Address"
                    value={email}
                    onChangeText={setEmail}
                    mode="outlined"
                    keyboardType="email-address"
                    autoCapitalize="none"
                    left={<TextInput.Icon icon="email" />}
                    style={styles.input}
                    theme={{ colors: { primary: theme.colors.primary } }}
                  />

                  <Button
                    mode="contained"
                    onPress={handleForgotPassword}
                    loading={loading}
                    disabled={loading}
                    style={[styles.resetButton, { backgroundColor: theme.colors.primary }]}
                    labelStyle={{ color: theme.colors.white }}
                  >
                    Send Reset Link
                  </Button>
                </>
              ) : (
                <View style={styles.successContainer}>
                  <Icon 
                    name="email-check" 
                    size={64} 
                    color={theme.colors.success} 
                    style={styles.successIcon}
                  />
                  <Text style={[styles.successTitle, { color: theme.colors.text }]}>
                    Email Sent!
                  </Text>
                  <Text style={[styles.successMessage, { color: theme.colors.text }]}>
                    We've sent password reset instructions to {email}
                  </Text>
                  <Text style={[styles.successNote, { color: theme.colors.text }]}>
                    Check your email and follow the link to reset your password. 
                    Don't forget to check your spam folder.
                  </Text>
                  
                  <Button
                    mode="outlined"
                    onPress={() => setEmailSent(false)}
                    style={styles.resendButton}
                    labelStyle={{ color: theme.colors.primary }}
                  >
                    Resend Email
                  </Button>
                </View>
              )}

              <Button
                mode="text"
                onPress={() => navigation.navigate('Login')}
                style={styles.backToLoginButton}
                labelStyle={{ color: theme.colors.primary }}
              >
                Back to Sign In
              </Button>
            </Card.Content>
          </Card>
        </Animatable.View>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    height: 300,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 50,
    position: 'relative',
  },
  backButton: {
    position: 'absolute',
    top: 50,
    left: 10,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginTop: 16,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    marginTop: 8,
    textAlign: 'center',
    opacity: 0.9,
    paddingHorizontal: 20,
  },
  content: {
    flex: 1,
    marginTop: -50,
  },
  resetCard: {
    margin: 20,
    borderRadius: 20,
    elevation: 8,
  },
  cardContent: {
    padding: 24,
  },
  instruction: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 24,
    textAlign: 'center',
    opacity: 0.8,
  },
  input: {
    marginBottom: 24,
  },
  resetButton: {
    paddingVertical: 8,
    borderRadius: 12,
    marginBottom: 16,
  },
  successContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  successIcon: {
    marginBottom: 16,
  },
  successTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  successMessage: {
    fontSize: 16,
    marginBottom: 16,
    textAlign: 'center',
    fontWeight: '500',
  },
  successNote: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 24,
    textAlign: 'center',
    opacity: 0.7,
  },
  resendButton: {
    marginBottom: 16,
  },
  backToLoginButton: {
    marginTop: 8,
  },
});

export default ForgotPasswordScreen;
