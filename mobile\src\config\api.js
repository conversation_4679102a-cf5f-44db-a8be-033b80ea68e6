export const API_CONFIG = {
  BASE_URL: 'http://localhost:5000/api',
  SOCKET_URL: 'http://localhost:5000',
  TIMEOUT: 10000,
};

// For Android emulator, use ******** instead of localhost
// For iOS simulator, localhost should work
// For physical devices, use your computer's IP address

export const getApiUrl = () => {
  // You can add logic here to detect environment and return appropriate URL
  return API_CONFIG.BASE_URL;
};
