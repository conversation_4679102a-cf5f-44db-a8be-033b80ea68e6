# 🗄️ HealthVision AI - Database Schema & ERD

## 📊 Entity Relationship Diagram

### 🏗️ Database Architecture Overview

Our database consists of **15 interconnected tables** designed to handle complex health data relationships, biometric monitoring, AI predictions, and user management.

## 📋 Table Specifications

### 1. **users** (Primary Entity)
```sql
- user_id (PK, UUID)
- email (UNIQUE, VARCHAR)
- password_hash (VARCHAR)
- first_name (VARCHAR)
- last_name (VARCHAR)
- date_of_birth (DATE)
- gender (ENUM: male, female, other)
- phone_number (VARCHAR)
- emergency_contact (VARCHAR)
- profile_picture (TEXT)
- subscription_type (ENUM: free, premium, enterprise)
- created_at (TIMESTAMP)
- updated_at (TIMESTAMP)
- is_active (BOOLEAN)
- last_login (TIMESTAMP)
```

### 2. **user_profiles** (Extended User Information)
```sql
- profile_id (PK, UUID)
- user_id (FK -> users.user_id)
- height (DECIMAL)
- weight (DECIMAL)
- blood_type (ENUM: A+, A-, B+, B-, AB+, AB-, O+, O-)
- allergies (TEXT)
- medical_conditions (TEXT)
- medications (TEXT)
- fitness_level (ENUM: beginner, intermediate, advanced, athlete)
- activity_level (ENUM: sedentary, lightly_active, moderately_active, very_active)
- health_goals (TEXT)
- preferred_language (VARCHAR)
- timezone (VARCHAR)
```

### 3. **biometric_data** (Core Health Metrics)
```sql
- biometric_id (PK, UUID)
- user_id (FK -> users.user_id)
- device_id (FK -> devices.device_id)
- measurement_type (ENUM: heart_rate, blood_pressure, glucose, temperature, oxygen_saturation)
- value (DECIMAL)
- unit (VARCHAR)
- timestamp (TIMESTAMP)
- location (VARCHAR)
- notes (TEXT)
- is_manual_entry (BOOLEAN)
- accuracy_score (DECIMAL)
```

### 4. **devices** (Connected Health Devices)
```sql
- device_id (PK, UUID)
- user_id (FK -> users.user_id)
- device_name (VARCHAR)
- device_type (ENUM: smartwatch, fitness_tracker, blood_pressure_monitor, glucometer, scale)
- brand (VARCHAR)
- model (VARCHAR)
- serial_number (VARCHAR)
- connection_status (ENUM: connected, disconnected, syncing)
- last_sync (TIMESTAMP)
- battery_level (INTEGER)
- firmware_version (VARCHAR)
```

### 5. **health_predictions** (AI-Generated Insights)
```sql
- prediction_id (PK, UUID)
- user_id (FK -> users.user_id)
- prediction_type (ENUM: disease_risk, wellness_score, lifestyle_recommendation)
- condition_name (VARCHAR)
- risk_score (DECIMAL)
- confidence_level (DECIMAL)
- prediction_date (TIMESTAMP)
- factors_considered (TEXT)
- recommendations (TEXT)
- model_version (VARCHAR)
- is_reviewed_by_doctor (BOOLEAN)
```

### 6. **wellness_plans** (Personalized Health Plans)
```sql
- plan_id (PK, UUID)
- user_id (FK -> users.user_id)
- created_by_doctor_id (FK -> healthcare_providers.provider_id)
- plan_name (VARCHAR)
- plan_type (ENUM: diet, exercise, medication, lifestyle)
- description (TEXT)
- start_date (DATE)
- end_date (DATE)
- status (ENUM: active, completed, paused, cancelled)
- goals (TEXT)
- progress_percentage (DECIMAL)
```

### 7. **healthcare_providers** (Medical Professionals)
```sql
- provider_id (PK, UUID)
- first_name (VARCHAR)
- last_name (VARCHAR)
- specialization (VARCHAR)
- license_number (VARCHAR)
- email (VARCHAR)
- phone_number (VARCHAR)
- clinic_name (VARCHAR)
- address (TEXT)
- consultation_fee (DECIMAL)
- rating (DECIMAL)
- years_experience (INTEGER)
- is_verified (BOOLEAN)
```

### 8. **consultations** (Telemedicine Sessions)
```sql
- consultation_id (PK, UUID)
- user_id (FK -> users.user_id)
- provider_id (FK -> healthcare_providers.provider_id)
- appointment_date (TIMESTAMP)
- duration_minutes (INTEGER)
- consultation_type (ENUM: video, audio, chat, in_person)
- status (ENUM: scheduled, in_progress, completed, cancelled)
- symptoms_reported (TEXT)
- diagnosis (TEXT)
- prescription (TEXT)
- follow_up_required (BOOLEAN)
- rating (INTEGER)
- cost (DECIMAL)
```

### 9. **wellness_challenges** (Gamification)
```sql
- challenge_id (PK, UUID)
- challenge_name (VARCHAR)
- description (TEXT)
- challenge_type (ENUM: steps, weight_loss, meditation, hydration, sleep)
- target_value (DECIMAL)
- duration_days (INTEGER)
- start_date (DATE)
- end_date (DATE)
- reward_points (INTEGER)
- difficulty_level (ENUM: easy, medium, hard, expert)
- is_public (BOOLEAN)
- created_by_admin (BOOLEAN)
```

### 10. **user_challenges** (User Challenge Participation)
```sql
- participation_id (PK, UUID)
- user_id (FK -> users.user_id)
- challenge_id (FK -> wellness_challenges.challenge_id)
- joined_date (TIMESTAMP)
- current_progress (DECIMAL)
- completion_percentage (DECIMAL)
- status (ENUM: active, completed, failed, abandoned)
- points_earned (INTEGER)
- completion_date (TIMESTAMP)
```

### 11. **health_reports** (Generated Reports)
```sql
- report_id (PK, UUID)
- user_id (FK -> users.user_id)
- report_type (ENUM: monthly_summary, annual_checkup, risk_assessment, progress_report)
- generated_date (TIMESTAMP)
- report_period_start (DATE)
- report_period_end (DATE)
- key_metrics (JSON)
- recommendations (TEXT)
- file_path (VARCHAR)
- is_shared_with_doctor (BOOLEAN)
```

### 12. **emergency_contacts** (Emergency Information)
```sql
- contact_id (PK, UUID)
- user_id (FK -> users.user_id)
- contact_name (VARCHAR)
- relationship (VARCHAR)
- phone_number (VARCHAR)
- email (VARCHAR)
- address (TEXT)
- is_primary (BOOLEAN)
- medical_power_of_attorney (BOOLEAN)
```

### 13. **notifications** (System Notifications)
```sql
- notification_id (PK, UUID)
- user_id (FK -> users.user_id)
- notification_type (ENUM: health_alert, appointment_reminder, challenge_update, system_update)
- title (VARCHAR)
- message (TEXT)
- priority (ENUM: low, medium, high, critical)
- is_read (BOOLEAN)
- created_at (TIMESTAMP)
- scheduled_for (TIMESTAMP)
- action_required (BOOLEAN)
```

### 14. **user_achievements** (Gamification Rewards)
```sql
- achievement_id (PK, UUID)
- user_id (FK -> users.user_id)
- achievement_type (ENUM: milestone, streak, improvement, challenge_completion)
- achievement_name (VARCHAR)
- description (TEXT)
- points_awarded (INTEGER)
- badge_icon (VARCHAR)
- earned_date (TIMESTAMP)
- is_public (BOOLEAN)
```

### 15. **system_analytics** (Platform Analytics)
```sql
- analytics_id (PK, UUID)
- metric_name (VARCHAR)
- metric_value (DECIMAL)
- metric_type (ENUM: user_engagement, health_improvement, prediction_accuracy, system_performance)
- recorded_date (TIMESTAMP)
- user_segment (VARCHAR)
- additional_data (JSON)
```

## 🔗 Key Relationships

1. **users** ↔ **user_profiles** (1:1)
2. **users** ↔ **biometric_data** (1:Many)
3. **users** ↔ **devices** (1:Many)
4. **users** ↔ **health_predictions** (1:Many)
5. **users** ↔ **wellness_plans** (1:Many)
6. **healthcare_providers** ↔ **consultations** (1:Many)
7. **users** ↔ **consultations** (1:Many)
8. **wellness_challenges** ↔ **user_challenges** (1:Many)
9. **users** ↔ **user_challenges** (1:Many)
10. **users** ↔ **health_reports** (1:Many)

## 📈 Advanced Features

- **Temporal Data**: Time-series biometric tracking
- **Predictive Analytics**: AI-driven health forecasting
- **Multi-device Integration**: Seamless device connectivity
- **Privacy Compliance**: HIPAA-compliant data handling
- **Scalable Architecture**: Designed for millions of users
