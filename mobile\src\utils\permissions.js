import { Platform, PermissionsAndroid, Alert } from 'react-native';
import { request, PERMISSIONS, RESULTS } from 'react-native-permissions';

export const requestPermissions = async () => {
  try {
    if (Platform.OS === 'android') {
      await requestAndroidPermissions();
    } else {
      await requestIOSPermissions();
    }
  } catch (error) {
    console.error('Error requesting permissions:', error);
  }
};

const requestAndroidPermissions = async () => {
  const permissions = [
    PermissionsAndroid.PERMISSIONS.CAMERA,
    PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
    PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE,
    PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
    PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
  ];

  try {
    const granted = await PermissionsAndroid.requestMultiple(permissions);
    
    Object.keys(granted).forEach(permission => {
      if (granted[permission] !== PermissionsAndroid.RESULTS.GRANTED) {
        console.log(`Permission ${permission} denied`);
      }
    });
  } catch (error) {
    console.error('Android permissions error:', error);
  }
};

const requestIOSPermissions = async () => {
  const permissions = [
    PERMISSIONS.IOS.CAMERA,
    PERMISSIONS.IOS.PHOTO_LIBRARY,
    PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
    PERMISSIONS.IOS.MICROPHONE,
  ];

  for (const permission of permissions) {
    try {
      const result = await request(permission);
      if (result !== RESULTS.GRANTED) {
        console.log(`Permission ${permission} denied`);
      }
    } catch (error) {
      console.error(`Error requesting ${permission}:`, error);
    }
  }
};

export const checkCameraPermission = async () => {
  if (Platform.OS === 'android') {
    const granted = await PermissionsAndroid.check(
      PermissionsAndroid.PERMISSIONS.CAMERA
    );
    return granted;
  } else {
    const result = await request(PERMISSIONS.IOS.CAMERA);
    return result === RESULTS.GRANTED;
  }
};

export const requestCameraPermission = async () => {
  if (Platform.OS === 'android') {
    const granted = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.CAMERA
    );
    return granted === PermissionsAndroid.RESULTS.GRANTED;
  } else {
    const result = await request(PERMISSIONS.IOS.CAMERA);
    return result === RESULTS.GRANTED;
  }
};
