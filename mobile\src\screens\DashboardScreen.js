import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  ScrollView,
  RefreshControl,
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  Animated
} from 'react-native';
import {
  Text,
  Card,
  Avatar,
  Button,
  FAB,
  Portal,
  Modal,
  IconButton
} from 'react-native-paper';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-native-chart-kit';
import LinearGradient from 'react-native-linear-gradient';
import { CircularProgress } from 'react-native-circular-progress';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import * as Animatable from 'react-native-animatable';

// Import contexts and services
import { useTheme } from '../contexts/ThemeContext';
import { useAuth } from '../contexts/AuthContext';
import { useHealthData } from '../contexts/HealthDataContext';
import { biometricService } from '../services/biometricService';
import { predictionService } from '../services/predictionService';

// Import components
import HealthMetricCard from '../components/HealthMetricCard';
import PredictionCard from '../components/PredictionCard';
import QuickActionButton from '../components/QuickActionButton';
import EmergencyAlert from '../components/EmergencyAlert';

const { width: screenWidth } = Dimensions.get('window');

const DashboardScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { user } = useAuth();
  const { latestReadings, emergencyAlerts } = useHealthData();
  
  const [refreshing, setRefreshing] = useState(false);
  const [dashboardData, setDashboardData] = useState({
    healthScore: 85,
    todaySteps: 8432,
    weeklyTrend: [],
    predictions: [],
    recentReadings: []
  });
  const [showQuickActions, setShowQuickActions] = useState(false);
  const [fadeAnim] = useState(new Animated.Value(0));

  useEffect(() => {
    loadDashboardData();
    
    // Animate dashboard on load
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
    }).start();
  }, []);

  const loadDashboardData = async () => {
    try {
      setRefreshing(true);
      
      // Load latest biometric data
      const biometricData = await biometricService.getLatestReadings();
      
      // Load health predictions
      const predictions = await predictionService.getLatestPredictions();
      
      // Load weekly trends
      const weeklyData = await biometricService.getWeeklyTrends();
      
      // Calculate health score
      const healthScore = calculateHealthScore(biometricData);
      
      setDashboardData({
        healthScore,
        todaySteps: biometricData.find(r => r.measurement_type === 'steps')?.value || 0,
        weeklyTrend: weeklyData,
        predictions: predictions.slice(0, 3),
        recentReadings: biometricData
      });
      
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const calculateHealthScore = (readings) => {
    // Simplified health score calculation
    let score = 100;
    
    readings.forEach(reading => {
      if (!reading.isWithinNormalRange) {
        score -= 10;
      }
      if (reading.flags?.requires_attention) {
        score -= 5;
      }
    });
    
    return Math.max(score, 0);
  };

  const onRefresh = useCallback(() => {
    loadDashboardData();
  }, []);

  const renderWelcomeHeader = () => (
    <Animatable.View animation="fadeInDown" duration={800}>
      <LinearGradient
        colors={[theme.colors.gradientStart, theme.colors.gradientEnd]}
        style={styles.headerGradient}
      >
        <View style={styles.headerContent}>
          <View style={styles.welcomeSection}>
            <Avatar.Image
              size={60}
              source={{ uri: user?.profile_picture || 'https://via.placeholder.com/60' }}
              style={styles.avatar}
            />
            <View style={styles.welcomeText}>
              <Text style={[styles.welcomeTitle, { color: theme.colors.white }]}>
                Good {getTimeOfDay()}, {user?.first_name}!
              </Text>
              <Text style={[styles.welcomeSubtitle, { color: theme.colors.white }]}>
                Let's check your health today
              </Text>
            </View>
          </View>
          
          <TouchableOpacity
            onPress={() => navigation.navigate('Notifications')}
            style={styles.notificationButton}
          >
            <Icon name="bell" size={24} color={theme.colors.white} />
            {emergencyAlerts.length > 0 && (
              <View style={styles.notificationBadge}>
                <Text style={styles.badgeText}>{emergencyAlerts.length}</Text>
              </View>
            )}
          </TouchableOpacity>
        </View>
      </LinearGradient>
    </Animatable.View>
  );

  const renderHealthScore = () => (
    <Animatable.View animation="fadeInUp" duration={800} delay={200}>
      <Card style={[styles.healthScoreCard, { backgroundColor: theme.colors.surface }]}>
        <Card.Content style={styles.healthScoreContent}>
          <View style={styles.scoreSection}>
            <CircularProgress
              size={120}
              width={8}
              fill={dashboardData.healthScore}
              tintColor={getScoreColor(dashboardData.healthScore)}
              backgroundColor={theme.colors.disabled}
              rotation={0}
            >
              {() => (
                <View style={styles.scoreCenter}>
                  <Text style={[styles.scoreValue, { color: theme.colors.text }]}>
                    {dashboardData.healthScore}
                  </Text>
                  <Text style={[styles.scoreLabel, { color: theme.colors.text }]}>
                    Health Score
                  </Text>
                </View>
              )}
            </CircularProgress>
          </View>
          
          <View style={styles.scoreDetails}>
            <Text style={[styles.scoreTitle, { color: theme.colors.text }]}>
              {getScoreMessage(dashboardData.healthScore)}
            </Text>
            <Text style={[styles.scoreDescription, { color: theme.colors.text }]}>
              Based on your recent biometric data and health trends
            </Text>
            
            <Button
              mode="contained"
              onPress={() => navigation.navigate('HealthReport')}
              style={styles.reportButton}
              labelStyle={{ color: theme.colors.white }}
            >
              View Full Report
            </Button>
          </View>
        </Card.Content>
      </Card>
    </Animatable.View>
  );

  const renderQuickStats = () => (
    <Animatable.View animation="fadeInUp" duration={800} delay={400}>
      <View style={styles.quickStatsContainer}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Today's Overview
        </Text>
        
        <View style={styles.statsGrid}>
          <HealthMetricCard
            title="Steps"
            value={dashboardData.todaySteps.toLocaleString()}
            unit=""
            icon="walk"
            color={theme.colors.steps}
            progress={dashboardData.todaySteps / 10000}
            onPress={() => navigation.navigate('BiometricDetails', { type: 'steps' })}
          />
          
          <HealthMetricCard
            title="Heart Rate"
            value={latestReadings.heart_rate?.value || '--'}
            unit="bpm"
            icon="heart-pulse"
            color={theme.colors.heartRate}
            onPress={() => navigation.navigate('BiometricDetails', { type: 'heart_rate' })}
          />
          
          <HealthMetricCard
            title="Sleep"
            value={latestReadings.sleep_duration?.value || '--'}
            unit="hrs"
            icon="sleep"
            color={theme.colors.sleep}
            onPress={() => navigation.navigate('BiometricDetails', { type: 'sleep_duration' })}
          />
          
          <HealthMetricCard
            title="Weight"
            value={latestReadings.weight?.value || '--'}
            unit="kg"
            icon="scale-bathroom"
            color={theme.colors.weight}
            onPress={() => navigation.navigate('BiometricDetails', { type: 'weight' })}
          />
        </View>
      </View>
    </Animatable.View>
  );

  const renderWeeklyTrend = () => (
    <Animatable.View animation="fadeInUp" duration={800} delay={600}>
      <Card style={[styles.chartCard, { backgroundColor: theme.colors.surface }]}>
        <Card.Content>
          <Text style={[styles.chartTitle, { color: theme.colors.text }]}>
            Weekly Health Trend
          </Text>
          
          {dashboardData.weeklyTrend.length > 0 && (
            <LineChart
              data={{
                labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                datasets: [{
                  data: dashboardData.weeklyTrend,
                  strokeWidth: 3,
                  color: (opacity = 1) => `rgba(0, 188, 212, ${opacity})`
                }]
              }}
              width={screenWidth - 60}
              height={200}
              chartConfig={{
                backgroundColor: theme.colors.surface,
                backgroundGradientFrom: theme.colors.surface,
                backgroundGradientTo: theme.colors.surface,
                decimalPlaces: 0,
                color: (opacity = 1) => theme.colors.primary,
                labelColor: (opacity = 1) => theme.colors.text,
                style: { borderRadius: 16 },
                propsForDots: {
                  r: "6",
                  strokeWidth: "2",
                  stroke: theme.colors.primary
                }
              }}
              bezier
              style={styles.chart}
            />
          )}
        </Card.Content>
      </Card>
    </Animatable.View>
  );

  const renderPredictions = () => (
    <Animatable.View animation="fadeInUp" duration={800} delay={800}>
      <View style={styles.predictionsContainer}>
        <View style={styles.sectionHeader}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            AI Health Insights
          </Text>
          <Button
            mode="text"
            onPress={() => navigation.navigate('Predictions')}
            labelStyle={{ color: theme.colors.primary }}
          >
            View All
          </Button>
        </View>
        
        {dashboardData.predictions.map((prediction, index) => (
          <PredictionCard
            key={prediction.prediction_id}
            prediction={prediction}
            onPress={() => navigation.navigate('PredictionDetails', { prediction })}
            style={{ marginBottom: theme.spacing.sm }}
          />
        ))}
      </View>
    </Animatable.View>
  );

  const getTimeOfDay = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Morning';
    if (hour < 17) return 'Afternoon';
    return 'Evening';
  };

  const getScoreColor = (score) => {
    if (score >= 80) return theme.colors.success;
    if (score >= 60) return theme.colors.warning;
    return theme.colors.error;
  };

  const getScoreMessage = (score) => {
    if (score >= 80) return 'Excellent Health!';
    if (score >= 60) return 'Good Health';
    if (score >= 40) return 'Fair Health';
    return 'Needs Attention';
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Animated.View style={{ opacity: fadeAnim }}>
        <ScrollView
          style={styles.scrollView}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          showsVerticalScrollIndicator={false}
        >
          {renderWelcomeHeader()}
          {renderHealthScore()}
          {renderQuickStats()}
          {renderWeeklyTrend()}
          {renderPredictions()}
          
          <View style={styles.bottomSpacing} />
        </ScrollView>
      </Animated.View>

      {/* Emergency Alerts */}
      {emergencyAlerts.map((alert, index) => (
        <EmergencyAlert
          key={alert.id}
          alert={alert}
          onDismiss={() => {/* Handle dismiss */}}
          style={{ top: 100 + (index * 80) }}
        />
      ))}

      {/* Floating Action Button */}
      <FAB
        icon="plus"
        style={[styles.fab, { backgroundColor: theme.colors.primary }]}
        onPress={() => setShowQuickActions(true)}
      />

      {/* Quick Actions Modal */}
      <Portal>
        <Modal
          visible={showQuickActions}
          onDismiss={() => setShowQuickActions(false)}
          contentContainerStyle={[styles.modalContent, { backgroundColor: theme.colors.surface }]}
        >
          <Text style={[styles.modalTitle, { color: theme.colors.text }]}>
            Quick Actions
          </Text>
          
          <View style={styles.quickActionsGrid}>
            <QuickActionButton
              icon="heart-pulse"
              label="Add Reading"
              onPress={() => {
                setShowQuickActions(false);
                navigation.navigate('AddReading');
              }}
            />
            <QuickActionButton
              icon="doctor"
              label="Book Consultation"
              onPress={() => {
                setShowQuickActions(false);
                navigation.navigate('BookConsultation');
              }}
            />
            <QuickActionButton
              icon="trophy"
              label="Join Challenge"
              onPress={() => {
                setShowQuickActions(false);
                navigation.navigate('Challenges');
              }}
            />
            <QuickActionButton
              icon="chart-line"
              label="View Analytics"
              onPress={() => {
                setShowQuickActions(false);
                navigation.navigate('Analytics');
              }}
            />
          </View>
        </Modal>
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  headerGradient: {
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  welcomeSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatar: {
    marginRight: 15,
  },
  welcomeText: {
    flex: 1,
  },
  welcomeTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  welcomeSubtitle: {
    fontSize: 14,
    opacity: 0.9,
  },
  notificationButton: {
    position: 'relative',
    padding: 8,
  },
  notificationBadge: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: '#FF6B6B',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  healthScoreCard: {
    margin: 20,
    marginTop: -30,
    borderRadius: 16,
    elevation: 8,
  },
  healthScoreContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
  },
  scoreSection: {
    marginRight: 20,
  },
  scoreCenter: {
    alignItems: 'center',
  },
  scoreValue: {
    fontSize: 32,
    fontWeight: 'bold',
  },
  scoreLabel: {
    fontSize: 12,
    opacity: 0.7,
  },
  scoreDetails: {
    flex: 1,
  },
  scoreTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  scoreDescription: {
    fontSize: 14,
    opacity: 0.7,
    marginBottom: 16,
  },
  reportButton: {
    borderRadius: 8,
  },
  quickStatsContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  chartCard: {
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 16,
    elevation: 4,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  chart: {
    borderRadius: 16,
  },
  predictionsContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  modalContent: {
    margin: 20,
    padding: 20,
    borderRadius: 16,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  bottomSpacing: {
    height: 100,
  },
});

export default DashboardScreen;
