import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Text, Card, ProgressBar } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import LinearGradient from 'react-native-linear-gradient';
import * as Animatable from 'react-native-animatable';
import { useTheme } from '../contexts/ThemeContext';

const HealthMetricCard = ({
  title,
  value,
  unit,
  icon,
  color,
  progress,
  trend,
  onPress,
  style,
}) => {
  const { theme } = useTheme();

  const getTrendIcon = () => {
    if (trend === 'up') return 'trending-up';
    if (trend === 'down') return 'trending-down';
    return 'trending-neutral';
  };

  const getTrendColor = () => {
    if (trend === 'up') return theme.colors.success;
    if (trend === 'down') return theme.colors.error;
    return theme.colors.text;
  };

  return (
    <Animatable.View animation="fadeInUp" duration={600}>
      <TouchableOpacity onPress={onPress} activeOpacity={0.8}>
        <Card style={[styles.card, { backgroundColor: theme.colors.surface }, style]}>
          <LinearGradient
            colors={[`${color}20`, `${color}10`]}
            style={styles.gradient}
          >
            <Card.Content style={styles.content}>
              <View style={styles.header}>
                <View style={[styles.iconContainer, { backgroundColor: `${color}20` }]}>
                  <Icon name={icon} size={24} color={color} />
                </View>
                {trend && (
                  <Icon 
                    name={getTrendIcon()} 
                    size={16} 
                    color={getTrendColor()} 
                  />
                )}
              </View>

              <View style={styles.valueContainer}>
                <Text style={[styles.value, { color: theme.colors.text }]}>
                  {value}
                </Text>
                {unit && (
                  <Text style={[styles.unit, { color: theme.colors.text }]}>
                    {unit}
                  </Text>
                )}
              </View>

              <Text style={[styles.title, { color: theme.colors.text }]}>
                {title}
              </Text>

              {progress !== undefined && (
                <View style={styles.progressContainer}>
                  <ProgressBar
                    progress={progress}
                    color={color}
                    style={styles.progressBar}
                  />
                  <Text style={[styles.progressText, { color: theme.colors.text }]}>
                    {Math.round(progress * 100)}%
                  </Text>
                </View>
              )}
            </Card.Content>
          </LinearGradient>
        </Card>
      </TouchableOpacity>
    </Animatable.View>
  );
};

const styles = StyleSheet.create({
  card: {
    width: '48%',
    marginBottom: 16,
    borderRadius: 16,
    elevation: 4,
    overflow: 'hidden',
  },
  gradient: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  valueContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 8,
  },
  value: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  unit: {
    fontSize: 14,
    marginLeft: 4,
    opacity: 0.7,
  },
  title: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  progressBar: {
    flex: 1,
    height: 4,
    borderRadius: 2,
  },
  progressText: {
    fontSize: 12,
    marginLeft: 8,
    opacity: 0.7,
  },
});

export default HealthMetricCard;
