import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Card,
  Checkbox,
  SegmentedButtons,
} from 'react-native-paper';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import DatePicker from 'react-native-date-picker';
import { showMessage } from 'react-native-flash-message';
import * as Animatable from 'react-native-animatable';

import { useTheme } from '../../contexts/ThemeContext';
import { useAuth } from '../../contexts/AuthContext';

const RegisterScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { register, loading } = useAuth();
  
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    email: '',
    password: '',
    confirmPassword: '',
    date_of_birth: new Date(),
    gender: 'male',
    phone_number: '',
  });
  
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [agreeToTerms, setAgreeToTerms] = useState(false);

  const updateFormData = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleRegister = async () => {
    // Validation
    if (!formData.first_name || !formData.last_name || !formData.email || !formData.password) {
      showMessage({
        message: 'Please fill in all required fields',
        type: 'warning',
      });
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      showMessage({
        message: 'Passwords do not match',
        type: 'warning',
      });
      return;
    }

    if (formData.password.length < 6) {
      showMessage({
        message: 'Password must be at least 6 characters',
        type: 'warning',
      });
      return;
    }

    if (!agreeToTerms) {
      showMessage({
        message: 'Please agree to the terms and conditions',
        type: 'warning',
      });
      return;
    }

    const { confirmPassword, ...registrationData } = formData;
    const result = await register(registrationData);
    
    if (!result.success) {
      showMessage({
        message: result.message || 'Registration failed',
        type: 'danger',
      });
    }
  };

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <LinearGradient
        colors={[theme.colors.gradientStart, theme.colors.gradientEnd]}
        style={styles.header}
      >
        <Animatable.View animation="fadeInDown" duration={1000}>
          <Icon name="account-plus" size={60} color={theme.colors.white} />
          <Text style={[styles.title, { color: theme.colors.white }]}>
            Join HealthVision AI
          </Text>
          <Text style={[styles.subtitle, { color: theme.colors.white }]}>
            Start your personalized health journey
          </Text>
        </Animatable.View>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Animatable.View animation="fadeInUp" duration={1000} delay={300}>
          <Card style={[styles.registerCard, { backgroundColor: theme.colors.surface }]}>
            <Card.Content style={styles.cardContent}>
              <Text style={[styles.registerTitle, { color: theme.colors.text }]}>
                Create Account
              </Text>

              <View style={styles.nameContainer}>
                <TextInput
                  label="First Name *"
                  value={formData.first_name}
                  onChangeText={(value) => updateFormData('first_name', value)}
                  mode="outlined"
                  style={[styles.input, styles.halfInput]}
                  theme={{ colors: { primary: theme.colors.primary } }}
                />
                <TextInput
                  label="Last Name *"
                  value={formData.last_name}
                  onChangeText={(value) => updateFormData('last_name', value)}
                  mode="outlined"
                  style={[styles.input, styles.halfInput]}
                  theme={{ colors: { primary: theme.colors.primary } }}
                />
              </View>

              <TextInput
                label="Email *"
                value={formData.email}
                onChangeText={(value) => updateFormData('email', value)}
                mode="outlined"
                keyboardType="email-address"
                autoCapitalize="none"
                left={<TextInput.Icon icon="email" />}
                style={styles.input}
                theme={{ colors: { primary: theme.colors.primary } }}
              />

              <TextInput
                label="Phone Number"
                value={formData.phone_number}
                onChangeText={(value) => updateFormData('phone_number', value)}
                mode="outlined"
                keyboardType="phone-pad"
                left={<TextInput.Icon icon="phone" />}
                style={styles.input}
                theme={{ colors: { primary: theme.colors.primary } }}
              />

              <View style={styles.genderContainer}>
                <Text style={[styles.genderLabel, { color: theme.colors.text }]}>
                  Gender *
                </Text>
                <SegmentedButtons
                  value={formData.gender}
                  onValueChange={(value) => updateFormData('gender', value)}
                  buttons={[
                    { value: 'male', label: 'Male' },
                    { value: 'female', label: 'Female' },
                    { value: 'other', label: 'Other' },
                  ]}
                  style={styles.genderButtons}
                />
              </View>

              <View style={styles.dateContainer}>
                <Text style={[styles.dateLabel, { color: theme.colors.text }]}>
                  Date of Birth *
                </Text>
                <Button
                  mode="outlined"
                  onPress={() => setShowDatePicker(true)}
                  icon="calendar"
                  style={styles.dateButton}
                >
                  {formData.date_of_birth.toLocaleDateString()}
                </Button>
              </View>

              <TextInput
                label="Password *"
                value={formData.password}
                onChangeText={(value) => updateFormData('password', value)}
                mode="outlined"
                secureTextEntry={!showPassword}
                left={<TextInput.Icon icon="lock" />}
                right={
                  <TextInput.Icon
                    icon={showPassword ? 'eye-off' : 'eye'}
                    onPress={() => setShowPassword(!showPassword)}
                  />
                }
                style={styles.input}
                theme={{ colors: { primary: theme.colors.primary } }}
              />

              <TextInput
                label="Confirm Password *"
                value={formData.confirmPassword}
                onChangeText={(value) => updateFormData('confirmPassword', value)}
                mode="outlined"
                secureTextEntry={!showConfirmPassword}
                left={<TextInput.Icon icon="lock-check" />}
                right={
                  <TextInput.Icon
                    icon={showConfirmPassword ? 'eye-off' : 'eye'}
                    onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                  />
                }
                style={styles.input}
                theme={{ colors: { primary: theme.colors.primary } }}
              />

              <View style={styles.termsContainer}>
                <Checkbox
                  status={agreeToTerms ? 'checked' : 'unchecked'}
                  onPress={() => setAgreeToTerms(!agreeToTerms)}
                  color={theme.colors.primary}
                />
                <Text style={[styles.termsText, { color: theme.colors.text }]}>
                  I agree to the{' '}
                  <Text style={{ color: theme.colors.primary }}>
                    Terms of Service
                  </Text>{' '}
                  and{' '}
                  <Text style={{ color: theme.colors.primary }}>
                    Privacy Policy
                  </Text>
                </Text>
              </View>

              <Button
                mode="contained"
                onPress={handleRegister}
                loading={loading}
                disabled={loading}
                style={[styles.registerButton, { backgroundColor: theme.colors.primary }]}
                labelStyle={{ color: theme.colors.white }}
              >
                Create Account
              </Button>

              <View style={styles.loginContainer}>
                <Text style={[styles.loginText, { color: theme.colors.text }]}>
                  Already have an account?{' '}
                </Text>
                <Button
                  mode="text"
                  onPress={() => navigation.navigate('Login')}
                  labelStyle={{ color: theme.colors.primary }}
                >
                  Sign In
                </Button>
              </View>
            </Card.Content>
          </Card>
        </Animatable.View>
      </ScrollView>

      <DatePicker
        modal
        open={showDatePicker}
        date={formData.date_of_birth}
        mode="date"
        maximumDate={new Date()}
        onConfirm={(date) => {
          setShowDatePicker(false);
          updateFormData('date_of_birth', date);
        }}
        onCancel={() => setShowDatePicker(false)}
      />
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 50,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 16,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
    opacity: 0.9,
  },
  content: {
    flex: 1,
    marginTop: -30,
  },
  registerCard: {
    margin: 20,
    borderRadius: 20,
    elevation: 8,
  },
  cardContent: {
    padding: 24,
  },
  registerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 24,
  },
  nameContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  input: {
    marginBottom: 16,
  },
  halfInput: {
    flex: 1,
    marginHorizontal: 4,
  },
  genderContainer: {
    marginBottom: 16,
  },
  genderLabel: {
    fontSize: 16,
    marginBottom: 8,
    fontWeight: '500',
  },
  genderButtons: {
    marginBottom: 8,
  },
  dateContainer: {
    marginBottom: 16,
  },
  dateLabel: {
    fontSize: 16,
    marginBottom: 8,
    fontWeight: '500',
  },
  dateButton: {
    justifyContent: 'flex-start',
  },
  termsContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 24,
  },
  termsText: {
    flex: 1,
    marginLeft: 8,
    fontSize: 14,
    lineHeight: 20,
  },
  registerButton: {
    paddingVertical: 8,
    borderRadius: 12,
    marginBottom: 24,
  },
  loginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loginText: {
    fontSize: 14,
  },
});

export default RegisterScreen;
