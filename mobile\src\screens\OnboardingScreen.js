import React, { useState, useRef } from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  FlatList,
} from 'react-native';
import {
  Text,
  <PERSON><PERSON>,
  Card,
} from 'react-native-paper';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import * as Animatable from 'react-native-animatable';

const { width, height } = Dimensions.get('window');

const onboardingData = [
  {
    id: 1,
    icon: 'heart-pulse',
    title: 'Monitor Your Health',
    description: 'Track your vital signs, biometric data, and health metrics in real-time with our advanced monitoring system.',
    color: '#E91E63',
  },
  {
    id: 2,
    icon: 'brain',
    title: 'AI-Powered Insights',
    description: 'Get personalized health predictions and recommendations powered by artificial intelligence and machine learning.',
    color: '#9C27B0',
  },
  {
    id: 3,
    icon: 'chart-line',
    title: 'Track Your Progress',
    description: 'Visualize your health journey with beautiful charts, trends, and analytics to stay motivated.',
    color: '#2196F3',
  },
  {
    id: 4,
    icon: 'doctor',
    title: 'Connect with Doctors',
    description: 'Book consultations, get medical advice, and connect with healthcare professionals seamlessly.',
    color: '#4CAF50',
  },
];

const OnboardingScreen = ({ onComplete }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const flatListRef = useRef(null);

  const handleNext = () => {
    if (currentIndex < onboardingData.length - 1) {
      const nextIndex = currentIndex + 1;
      setCurrentIndex(nextIndex);
      flatListRef.current?.scrollToIndex({ index: nextIndex, animated: true });
    } else {
      onComplete();
    }
  };

  const handleSkip = () => {
    onComplete();
  };

  const renderOnboardingItem = ({ item, index }) => (
    <View style={styles.slide}>
      <LinearGradient
        colors={[item.color, `${item.color}80`]}
        style={styles.iconContainer}
      >
        <Icon name={item.icon} size={80} color="#FFFFFF" />
      </LinearGradient>
      
      <View style={styles.textContainer}>
        <Text style={styles.title}>{item.title}</Text>
        <Text style={styles.description}>{item.description}</Text>
      </View>
    </View>
  );

  const renderPagination = () => (
    <View style={styles.pagination}>
      {onboardingData.map((_, index) => (
        <View
          key={index}
          style={[
            styles.paginationDot,
            {
              backgroundColor: index === currentIndex ? '#00BCD4' : '#E0E0E0',
              width: index === currentIndex ? 24 : 8,
            },
          ]}
        />
      ))}
    </View>
  );

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#00BCD4', '#4CAF50']}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>HealthVision AI</Text>
          <Button
            mode="text"
            onPress={handleSkip}
            labelStyle={styles.skipButton}
          >
            Skip
          </Button>
        </View>
      </LinearGradient>

      <View style={styles.content}>
        <FlatList
          ref={flatListRef}
          data={onboardingData}
          renderItem={renderOnboardingItem}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onMomentumScrollEnd={(event) => {
            const index = Math.round(event.nativeEvent.contentOffset.x / width);
            setCurrentIndex(index);
          }}
          keyExtractor={(item) => item.id.toString()}
        />

        {renderPagination()}

        <View style={styles.buttonContainer}>
          <Button
            mode="contained"
            onPress={handleNext}
            style={styles.nextButton}
            labelStyle={styles.nextButtonLabel}
          >
            {currentIndex === onboardingData.length - 1 ? 'Get Started' : 'Next'}
          </Button>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    height: 120,
    justifyContent: 'flex-end',
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  skipButton: {
    color: '#FFFFFF',
    fontSize: 16,
  },
  content: {
    flex: 1,
    justifyContent: 'space-between',
  },
  slide: {
    width,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  iconContainer: {
    width: 160,
    height: 160,
    borderRadius: 80,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 40,
  },
  textContainer: {
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333333',
    textAlign: 'center',
    marginBottom: 20,
  },
  description: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 24,
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 30,
  },
  paginationDot: {
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  buttonContainer: {
    paddingHorizontal: 40,
    paddingBottom: 40,
  },
  nextButton: {
    backgroundColor: '#00BCD4',
    paddingVertical: 8,
    borderRadius: 12,
  },
  nextButtonLabel: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default OnboardingScreen;
