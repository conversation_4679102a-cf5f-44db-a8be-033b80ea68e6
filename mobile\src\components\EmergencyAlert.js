import React from 'react';
import { View, StyleSheet, TouchableOpacity, Dimensions } from 'react-native';
import { Text, IconButton } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import LinearGradient from 'react-native-linear-gradient';
import * as Animatable from 'react-native-animatable';
import { useTheme } from '../contexts/ThemeContext';

const { width } = Dimensions.get('window');

const EmergencyAlert = ({ alert, onDismiss, onPress, style }) => {
  const { theme } = useTheme();

  const getAlertIcon = (type) => {
    const iconMap = {
      critical_reading: 'alert-circle',
      emergency: 'alert',
      medication_reminder: 'pill',
      appointment: 'calendar-clock',
    };
    return iconMap[type] || 'alert-circle';
  };

  const getAlertColor = (type) => {
    const colorMap = {
      critical_reading: theme.colors.error,
      emergency: theme.colors.error,
      medication_reminder: theme.colors.warning,
      appointment: theme.colors.info,
    };
    return colorMap[type] || theme.colors.error;
  };

  const alertColor = getAlertColor(alert.type);

  return (
    <Animatable.View 
      animation="slideInDown" 
      duration={500}
      style={[styles.container, style]}
    >
      <TouchableOpacity onPress={onPress} activeOpacity={0.9}>
        <LinearGradient
          colors={[alertColor, `${alertColor}E0`]}
          style={styles.gradient}
        >
          <View style={styles.content}>
            <View style={styles.iconContainer}>
              <Icon 
                name={getAlertIcon(alert.type)} 
                size={24} 
                color={theme.colors.white} 
              />
            </View>
            
            <View style={styles.textContainer}>
              <Text style={[styles.title, { color: theme.colors.white }]}>
                Health Alert
              </Text>
              <Text style={[styles.message, { color: theme.colors.white }]}>
                {alert.message || 'Critical health reading detected'}
              </Text>
              {alert.measurement_type && (
                <Text style={[styles.details, { color: theme.colors.white }]}>
                  {alert.measurement_type}: {alert.value}
                </Text>
              )}
            </View>
            
            <IconButton
              icon="close"
              size={20}
              iconColor={theme.colors.white}
              onPress={onDismiss}
              style={styles.closeButton}
            />
          </View>
        </LinearGradient>
      </TouchableOpacity>
    </Animatable.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 20,
    right: 20,
    zIndex: 1000,
  },
  gradient: {
    borderRadius: 12,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  message: {
    fontSize: 14,
    lineHeight: 18,
    marginBottom: 2,
  },
  details: {
    fontSize: 12,
    opacity: 0.9,
  },
  closeButton: {
    margin: 0,
  },
});

export default EmergencyAlert;
