import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Image,
} from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Card,
  Checkbox,
  Divider,
} from 'react-native-paper';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { showMessage } from 'react-native-flash-message';
import * as Animatable from 'react-native-animatable';

import { useTheme } from '../../contexts/ThemeContext';
import { useAuth } from '../../contexts/AuthContext';

const LoginScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { login, loading } = useAuth();
  
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const handleLogin = async () => {
    if (!email || !password) {
      showMessage({
        message: 'Please fill in all fields',
        type: 'warning',
      });
      return;
    }

    const result = await login(email, password);
    
    if (!result.success) {
      showMessage({
        message: result.message || 'Login failed',
        type: 'danger',
      });
    }
  };

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <LinearGradient
        colors={[theme.colors.gradientStart, theme.colors.gradientEnd]}
        style={styles.header}
      >
        <Animatable.View animation="fadeInDown" duration={1000}>
          <Icon name="heart-pulse" size={80} color={theme.colors.white} />
          <Text style={[styles.title, { color: theme.colors.white }]}>
            HealthVision AI
          </Text>
          <Text style={[styles.subtitle, { color: theme.colors.white }]}>
            Your Personal Health Assistant
          </Text>
        </Animatable.View>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Animatable.View animation="fadeInUp" duration={1000} delay={300}>
          <Card style={[styles.loginCard, { backgroundColor: theme.colors.surface }]}>
            <Card.Content style={styles.cardContent}>
              <Text style={[styles.loginTitle, { color: theme.colors.text }]}>
                Welcome Back
              </Text>
              <Text style={[styles.loginSubtitle, { color: theme.colors.text }]}>
                Sign in to continue your health journey
              </Text>

              <View style={styles.inputContainer}>
                <TextInput
                  label="Email"
                  value={email}
                  onChangeText={setEmail}
                  mode="outlined"
                  keyboardType="email-address"
                  autoCapitalize="none"
                  left={<TextInput.Icon icon="email" />}
                  style={styles.input}
                  theme={{ colors: { primary: theme.colors.primary } }}
                />

                <TextInput
                  label="Password"
                  value={password}
                  onChangeText={setPassword}
                  mode="outlined"
                  secureTextEntry={!showPassword}
                  left={<TextInput.Icon icon="lock" />}
                  right={
                    <TextInput.Icon
                      icon={showPassword ? 'eye-off' : 'eye'}
                      onPress={() => setShowPassword(!showPassword)}
                    />
                  }
                  style={styles.input}
                  theme={{ colors: { primary: theme.colors.primary } }}
                />
              </View>

              <View style={styles.optionsContainer}>
                <View style={styles.checkboxContainer}>
                  <Checkbox
                    status={rememberMe ? 'checked' : 'unchecked'}
                    onPress={() => setRememberMe(!rememberMe)}
                    color={theme.colors.primary}
                  />
                  <Text style={[styles.checkboxLabel, { color: theme.colors.text }]}>
                    Remember me
                  </Text>
                </View>

                <Button
                  mode="text"
                  onPress={() => navigation.navigate('ForgotPassword')}
                  labelStyle={{ color: theme.colors.primary }}
                >
                  Forgot Password?
                </Button>
              </View>

              <Button
                mode="contained"
                onPress={handleLogin}
                loading={loading}
                disabled={loading}
                style={[styles.loginButton, { backgroundColor: theme.colors.primary }]}
                labelStyle={{ color: theme.colors.white }}
              >
                Sign In
              </Button>

              <Divider style={styles.divider} />

              <View style={styles.socialContainer}>
                <Text style={[styles.socialText, { color: theme.colors.text }]}>
                  Or continue with
                </Text>
                
                <View style={styles.socialButtons}>
                  <Button
                    mode="outlined"
                    icon="google"
                    style={styles.socialButton}
                    labelStyle={{ color: theme.colors.text }}
                  >
                    Google
                  </Button>
                  <Button
                    mode="outlined"
                    icon="apple"
                    style={styles.socialButton}
                    labelStyle={{ color: theme.colors.text }}
                  >
                    Apple
                  </Button>
                </View>
              </View>

              <View style={styles.signupContainer}>
                <Text style={[styles.signupText, { color: theme.colors.text }]}>
                  Don't have an account?{' '}
                </Text>
                <Button
                  mode="text"
                  onPress={() => navigation.navigate('Register')}
                  labelStyle={{ color: theme.colors.primary }}
                >
                  Sign Up
                </Button>
              </View>
            </Card.Content>
          </Card>
        </Animatable.View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    height: 250,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 50,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginTop: 16,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    marginTop: 8,
    textAlign: 'center',
    opacity: 0.9,
  },
  content: {
    flex: 1,
    marginTop: -30,
  },
  loginCard: {
    margin: 20,
    borderRadius: 20,
    elevation: 8,
  },
  cardContent: {
    padding: 24,
  },
  loginTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  loginSubtitle: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 32,
    opacity: 0.7,
  },
  inputContainer: {
    marginBottom: 16,
  },
  input: {
    marginBottom: 16,
  },
  optionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkboxLabel: {
    marginLeft: 8,
    fontSize: 14,
  },
  loginButton: {
    paddingVertical: 8,
    borderRadius: 12,
    marginBottom: 24,
  },
  divider: {
    marginBottom: 24,
  },
  socialContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  socialText: {
    fontSize: 14,
    marginBottom: 16,
    opacity: 0.7,
  },
  socialButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  socialButton: {
    flex: 1,
    marginHorizontal: 8,
  },
  signupContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  signupText: {
    fontSize: 14,
  },
});

export default LoginScreen;
