import { DefaultTheme, DarkTheme } from 'react-native-paper';

// Color palette
const colors = {
  // Primary colors - Health & Medical theme
  primary: '#00BCD4', // Cyan - represents health, cleanliness, medical
  primaryDark: '#0097A7',
  primaryLight: '#4DD0E1',
  
  // Secondary colors - Wellness & Energy
  secondary: '#4CAF50', // Green - represents wellness, growth, vitality
  secondaryDark: '#388E3C',
  secondaryLight: '#81C784',
  
  // Accent colors
  accent: '#FF6B6B', // Coral - for alerts, important actions
  accentDark: '#E53E3E',
  accentLight: '#FF8A80',
  
  // Status colors
  success: '#4CAF50',
  warning: '#FF9800',
  error: '#F44336',
  info: '#2196F3',
  
  // Neutral colors
  white: '#FFFFFF',
  black: '#000000',
  
  // Light theme grays
  lightGray: '#F5F5F5',
  mediumGray: '#E0E0E0',
  darkGray: '#757575',
  
  // Dark theme grays
  darkSurface: '#121212',
  darkBackground: '#1E1E1E',
  darkCard: '#2D2D2D',
  
  // Health-specific colors
  heartRate: '#E91E63', // Pink for heart rate
  bloodPressure: '#9C27B0', // Purple for blood pressure
  glucose: '#FF9800', // Orange for glucose
  temperature: '#F44336', // Red for temperature
  oxygen: '#2196F3', // Blue for oxygen saturation
  weight: '#795548', // Brown for weight
  steps: '#4CAF50', // Green for steps
  sleep: '#3F51B5', // Indigo for sleep
  
  // Gradient colors
  gradientStart: '#00BCD4',
  gradientEnd: '#4CAF50',
  
  // Chart colors
  chartColors: [
    '#00BCD4', '#4CAF50', '#FF6B6B', '#FF9800',
    '#9C27B0', '#2196F3', '#795548', '#607D8B'
  ]
};

// Light theme
export const lightTheme = {
  ...DefaultTheme,
  dark: false,
  colors: {
    ...DefaultTheme.colors,
    primary: colors.primary,
    accent: colors.accent,
    background: colors.white,
    surface: colors.white,
    card: colors.white,
    text: colors.black,
    onSurface: colors.black,
    disabled: colors.mediumGray,
    placeholder: colors.darkGray,
    backdrop: 'rgba(0, 0, 0, 0.5)',
    notification: colors.accent,
    
    // Custom colors
    secondary: colors.secondary,
    success: colors.success,
    warning: colors.warning,
    error: colors.error,
    info: colors.info,
    
    // Health colors
    heartRate: colors.heartRate,
    bloodPressure: colors.bloodPressure,
    glucose: colors.glucose,
    temperature: colors.temperature,
    oxygen: colors.oxygen,
    weight: colors.weight,
    steps: colors.steps,
    sleep: colors.sleep,
    
    // UI colors
    border: colors.mediumGray,
    divider: colors.lightGray,
    shadow: 'rgba(0, 0, 0, 0.1)',
    overlay: 'rgba(0, 0, 0, 0.3)',
    
    // Gradient
    gradientStart: colors.gradientStart,
    gradientEnd: colors.gradientEnd,
    
    // Chart colors
    chartColors: colors.chartColors
  },
  
  // Custom theme properties
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48
  },
  
  borderRadius: {
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
    round: 50
  },
  
  typography: {
    h1: {
      fontSize: 32,
      fontWeight: 'bold',
      lineHeight: 40
    },
    h2: {
      fontSize: 28,
      fontWeight: 'bold',
      lineHeight: 36
    },
    h3: {
      fontSize: 24,
      fontWeight: '600',
      lineHeight: 32
    },
    h4: {
      fontSize: 20,
      fontWeight: '600',
      lineHeight: 28
    },
    h5: {
      fontSize: 18,
      fontWeight: '500',
      lineHeight: 24
    },
    h6: {
      fontSize: 16,
      fontWeight: '500',
      lineHeight: 22
    },
    body1: {
      fontSize: 16,
      fontWeight: 'normal',
      lineHeight: 24
    },
    body2: {
      fontSize: 14,
      fontWeight: 'normal',
      lineHeight: 20
    },
    caption: {
      fontSize: 12,
      fontWeight: 'normal',
      lineHeight: 16
    },
    button: {
      fontSize: 16,
      fontWeight: '600',
      textTransform: 'uppercase'
    }
  },
  
  shadows: {
    small: {
      shadowColor: colors.black,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2
    },
    medium: {
      shadowColor: colors.black,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.15,
      shadowRadius: 8,
      elevation: 4
    },
    large: {
      shadowColor: colors.black,
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.2,
      shadowRadius: 16,
      elevation: 8
    }
  }
};

// Dark theme
export const darkTheme = {
  ...DarkTheme,
  dark: true,
  colors: {
    ...DarkTheme.colors,
    primary: colors.primaryLight,
    accent: colors.accentLight,
    background: colors.darkBackground,
    surface: colors.darkSurface,
    card: colors.darkCard,
    text: colors.white,
    onSurface: colors.white,
    disabled: colors.darkGray,
    placeholder: colors.mediumGray,
    backdrop: 'rgba(255, 255, 255, 0.1)',
    notification: colors.accentLight,
    
    // Custom colors
    secondary: colors.secondaryLight,
    success: colors.success,
    warning: colors.warning,
    error: colors.error,
    info: colors.info,
    
    // Health colors (slightly lighter for dark theme)
    heartRate: '#F48FB1',
    bloodPressure: '#CE93D8',
    glucose: '#FFB74D',
    temperature: '#EF5350',
    oxygen: '#64B5F6',
    weight: '#A1887F',
    steps: '#81C784',
    sleep: '#7986CB',
    
    // UI colors
    border: colors.darkGray,
    divider: colors.darkCard,
    shadow: 'rgba(255, 255, 255, 0.1)',
    overlay: 'rgba(255, 255, 255, 0.1)',
    
    // Gradient
    gradientStart: colors.primaryLight,
    gradientEnd: colors.secondaryLight,
    
    // Chart colors (lighter versions for dark theme)
    chartColors: [
      '#4DD0E1', '#81C784', '#FF8A80', '#FFB74D',
      '#CE93D8', '#64B5F6', '#A1887F', '#90A4AE'
    ]
  },
  
  // Inherit other properties from light theme
  spacing: lightTheme.spacing,
  borderRadius: lightTheme.borderRadius,
  typography: {
    ...lightTheme.typography,
    // Adjust typography for dark theme if needed
  },
  
  shadows: {
    small: {
      shadowColor: colors.white,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2
    },
    medium: {
      shadowColor: colors.white,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.15,
      shadowRadius: 8,
      elevation: 4
    },
    large: {
      shadowColor: colors.white,
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.2,
      shadowRadius: 16,
      elevation: 8
    }
  }
};

// Theme utilities
export const getHealthColor = (measurementType, theme) => {
  const colorMap = {
    heart_rate: theme.colors.heartRate,
    blood_pressure_systolic: theme.colors.bloodPressure,
    blood_pressure_diastolic: theme.colors.bloodPressure,
    glucose: theme.colors.glucose,
    temperature: theme.colors.temperature,
    oxygen_saturation: theme.colors.oxygen,
    weight: theme.colors.weight,
    steps: theme.colors.steps,
    sleep_duration: theme.colors.sleep,
    sleep_quality: theme.colors.sleep
  };
  
  return colorMap[measurementType] || theme.colors.primary;
};

export const getRiskColor = (riskLevel, theme) => {
  const colorMap = {
    low: theme.colors.success,
    moderate: theme.colors.warning,
    high: theme.colors.error,
    critical: theme.colors.accent
  };
  
  return colorMap[riskLevel] || theme.colors.primary;
};
