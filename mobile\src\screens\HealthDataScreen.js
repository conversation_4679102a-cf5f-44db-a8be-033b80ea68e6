import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import {
  Text,
  Card,
  FAB,
  Searchbar,
  SegmentedButtons,
} from 'react-native-paper';
import { LineChart } from 'react-native-chart-kit';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import * as Animatable from 'react-native-animatable';

import { useTheme } from '../contexts/ThemeContext';
import { useHealthData } from '../contexts/HealthDataContext';
import { biometricService } from '../services/biometricService';
import HealthMetricCard from '../components/HealthMetricCard';

const { width: screenWidth } = Dimensions.get('window');

const HealthDataScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { latestReadings, refreshData } = useHealthData();
  
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedPeriod, setSelectedPeriod] = useState('week');
  const [chartData, setChartData] = useState(null);
  const [selectedMetric, setSelectedMetric] = useState('heart_rate');

  useEffect(() => {
    loadChartData();
  }, [selectedMetric, selectedPeriod]);

  const loadChartData = async () => {
    try {
      const trends = await biometricService.getTrends(selectedMetric, selectedPeriod);
      if (trends.success && trends.data.readings.length > 0) {
        const data = trends.data.readings.map(reading => reading.value);
        const labels = trends.data.readings.map((reading, index) => {
          const date = new Date(reading.timestamp);
          return selectedPeriod === 'week' ? 
            date.toLocaleDateString('en-US', { weekday: 'short' }) :
            date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        });

        setChartData({
          labels: labels.slice(-7), // Show last 7 data points
          datasets: [{
            data: data.slice(-7),
            strokeWidth: 3,
            color: (opacity = 1) => `rgba(0, 188, 212, ${opacity})`
          }]
        });
      }
    } catch (error) {
      console.error('Error loading chart data:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await refreshData();
    await loadChartData();
    setRefreshing(false);
  };

  const healthMetrics = [
    {
      key: 'heart_rate',
      title: 'Heart Rate',
      icon: 'heart-pulse',
      color: theme.colors.heartRate,
      unit: 'bpm'
    },
    {
      key: 'blood_pressure_systolic',
      title: 'Blood Pressure',
      icon: 'water',
      color: theme.colors.bloodPressure,
      unit: 'mmHg'
    },
    {
      key: 'glucose',
      title: 'Blood Glucose',
      icon: 'diabetes',
      color: theme.colors.glucose,
      unit: 'mg/dL'
    },
    {
      key: 'temperature',
      title: 'Temperature',
      icon: 'thermometer',
      color: theme.colors.temperature,
      unit: '°C'
    },
    {
      key: 'oxygen_saturation',
      title: 'Oxygen Saturation',
      icon: 'lungs',
      color: theme.colors.oxygen,
      unit: '%'
    },
    {
      key: 'weight',
      title: 'Weight',
      icon: 'scale-bathroom',
      color: theme.colors.weight,
      unit: 'kg'
    },
    {
      key: 'steps',
      title: 'Steps',
      icon: 'walk',
      color: theme.colors.steps,
      unit: 'steps'
    },
    {
      key: 'sleep_duration',
      title: 'Sleep',
      icon: 'sleep',
      color: theme.colors.sleep,
      unit: 'hrs'
    },
  ];

  const filteredMetrics = healthMetrics.filter(metric =>
    metric.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const renderHeader = () => (
    <Animatable.View animation="fadeInDown" duration={800}>
      <View style={[styles.header, { backgroundColor: theme.colors.primary }]}>
        <Text style={[styles.headerTitle, { color: theme.colors.white }]}>
          Health Data
        </Text>
        <Text style={[styles.headerSubtitle, { color: theme.colors.white }]}>
          Track your vital signs and metrics
        </Text>
      </View>
    </Animatable.View>
  );

  const renderSearchAndFilter = () => (
    <Animatable.View animation="fadeInUp" duration={800} delay={200}>
      <View style={styles.searchContainer}>
        <Searchbar
          placeholder="Search health metrics..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={[styles.searchbar, { backgroundColor: theme.colors.surface }]}
          iconColor={theme.colors.primary}
        />
      </View>
    </Animatable.View>
  );

  const renderChart = () => (
    <Animatable.View animation="fadeInUp" duration={800} delay={400}>
      <Card style={[styles.chartCard, { backgroundColor: theme.colors.surface }]}>
        <Card.Content>
          <View style={styles.chartHeader}>
            <Text style={[styles.chartTitle, { color: theme.colors.text }]}>
              {healthMetrics.find(m => m.key === selectedMetric)?.title} Trend
            </Text>
            
            <SegmentedButtons
              value={selectedPeriod}
              onValueChange={setSelectedPeriod}
              buttons={[
                { value: 'week', label: 'Week' },
                { value: 'month', label: 'Month' },
              ]}
              style={styles.periodButtons}
            />
          </View>

          <View style={styles.metricSelector}>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              {healthMetrics.map((metric) => (
                <TouchableOpacity
                  key={metric.key}
                  onPress={() => setSelectedMetric(metric.key)}
                  style={[
                    styles.metricButton,
                    {
                      backgroundColor: selectedMetric === metric.key 
                        ? metric.color 
                        : `${metric.color}20`
                    }
                  ]}
                >
                  <Icon 
                    name={metric.icon} 
                    size={20} 
                    color={selectedMetric === metric.key ? theme.colors.white : metric.color} 
                  />
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>

          {chartData && (
            <LineChart
              data={chartData}
              width={screenWidth - 60}
              height={220}
              chartConfig={{
                backgroundColor: theme.colors.surface,
                backgroundGradientFrom: theme.colors.surface,
                backgroundGradientTo: theme.colors.surface,
                decimalPlaces: 1,
                color: (opacity = 1) => theme.colors.primary,
                labelColor: (opacity = 1) => theme.colors.text,
                style: { borderRadius: 16 },
                propsForDots: {
                  r: "6",
                  strokeWidth: "2",
                  stroke: theme.colors.primary
                }
              }}
              bezier
              style={styles.chart}
            />
          )}
        </Card.Content>
      </Card>
    </Animatable.View>
  );

  const renderMetricsGrid = () => (
    <Animatable.View animation="fadeInUp" duration={800} delay={600}>
      <View style={styles.metricsContainer}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Latest Readings
        </Text>
        
        <View style={styles.metricsGrid}>
          {filteredMetrics.map((metric, index) => (
            <HealthMetricCard
              key={metric.key}
              title={metric.title}
              value={latestReadings[metric.key]?.value || '--'}
              unit={metric.unit}
              icon={metric.icon}
              color={metric.color}
              onPress={() => navigation.navigate('BiometricDetails', { 
                type: metric.key,
                title: metric.title 
              })}
            />
          ))}
        </View>
      </View>
    </Animatable.View>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {renderHeader()}
      
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderSearchAndFilter()}
        {renderChart()}
        {renderMetricsGrid()}
        
        <View style={styles.bottomSpacing} />
      </ScrollView>

      <FAB
        icon="plus"
        style={[styles.fab, { backgroundColor: theme.colors.primary }]}
        onPress={() => navigation.navigate('AddReading')}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    opacity: 0.9,
  },
  content: {
    flex: 1,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  searchbar: {
    elevation: 2,
  },
  chartCard: {
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 16,
    elevation: 4,
  },
  chartHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  periodButtons: {
    flex: 0,
  },
  metricSelector: {
    marginBottom: 16,
  },
  metricButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  chart: {
    borderRadius: 16,
  },
  metricsContainer: {
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  bottomSpacing: {
    height: 100,
  },
});

export default HealthDataScreen;
