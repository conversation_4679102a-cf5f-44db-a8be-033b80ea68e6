const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');

const biometricDataSchema = new mongoose.Schema({
  biometric_id: {
    type: String,
    default: uuidv4,
    unique: true,
    required: true
  },
  user_id: {
    type: String,
    required: true,
    ref: 'User'
  },
  device_id: {
    type: String,
    ref: 'Device',
    default: null
  },
  measurement_type: {
    type: String,
    enum: [
      'heart_rate',
      'blood_pressure_systolic',
      'blood_pressure_diastolic',
      'glucose',
      'temperature',
      'oxygen_saturation',
      'weight',
      'body_fat_percentage',
      'muscle_mass',
      'steps',
      'calories_burned',
      'sleep_duration',
      'sleep_quality',
      'stress_level',
      'hydration_level'
    ],
    required: true
  },
  value: {
    type: Number,
    required: true,
    min: 0
  },
  unit: {
    type: String,
    required: true,
    enum: [
      'bpm',        // beats per minute
      'mmHg',       // millimeters of mercury
      'mg/dL',      // milligrams per deciliter
      'mmol/L',     // millimoles per liter
      '°C',         // celsius
      '°F',         // fahrenheit
      '%',          // percentage
      'kg',         // kilograms
      'lbs',        // pounds
      'steps',      // step count
      'kcal',       // kilocalories
      'hours',      // hours
      'minutes',    // minutes
      'score',      // quality score
      'liters',     // liters
      'ml'          // milliliters
    ]
  },
  timestamp: {
    type: Date,
    default: Date.now,
    required: true
  },
  location: {
    type: String,
    trim: true,
    default: null
  },
  notes: {
    type: String,
    trim: true,
    maxlength: 500
  },
  is_manual_entry: {
    type: Boolean,
    default: false
  },
  accuracy_score: {
    type: Number,
    min: 0,
    max: 100,
    default: 95
  },
  context: {
    activity_level: {
      type: String,
      enum: ['resting', 'light_activity', 'moderate_activity', 'vigorous_activity'],
      default: 'resting'
    },
    meal_timing: {
      type: String,
      enum: ['fasting', 'before_meal', 'after_meal', '2_hours_post_meal'],
      default: null
    },
    medication_taken: {
      type: Boolean,
      default: false
    },
    stress_level: {
      type: String,
      enum: ['low', 'moderate', 'high'],
      default: 'low'
    }
  },
  flags: {
    is_anomaly: {
      type: Boolean,
      default: false
    },
    requires_attention: {
      type: Boolean,
      default: false
    },
    is_emergency: {
      type: Boolean,
      default: false
    }
  },
  processed_by_ai: {
    type: Boolean,
    default: false
  },
  ai_insights: {
    trend: {
      type: String,
      enum: ['improving', 'stable', 'declining', 'fluctuating'],
      default: null
    },
    recommendation: {
      type: String,
      maxlength: 1000,
      default: null
    },
    risk_level: {
      type: String,
      enum: ['low', 'moderate', 'high', 'critical'],
      default: 'low'
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
biometricDataSchema.index({ user_id: 1, timestamp: -1 });
biometricDataSchema.index({ user_id: 1, measurement_type: 1, timestamp: -1 });
biometricDataSchema.index({ measurement_type: 1, timestamp: -1 });
biometricDataSchema.index({ 'flags.is_emergency': 1 });
biometricDataSchema.index({ 'flags.requires_attention': 1 });

// Virtual for formatted timestamp
biometricDataSchema.virtual('formatted_timestamp').get(function() {
  return this.timestamp.toISOString();
});

// Virtual for value with unit
biometricDataSchema.virtual('display_value').get(function() {
  return `${this.value} ${this.unit}`;
});

// Static method to get latest reading for a user and measurement type
biometricDataSchema.statics.getLatestReading = function(userId, measurementType) {
  return this.findOne({
    user_id: userId,
    measurement_type: measurementType
  }).sort({ timestamp: -1 });
};

// Static method to get readings within date range
biometricDataSchema.statics.getReadingsInRange = function(userId, measurementType, startDate, endDate) {
  return this.find({
    user_id: userId,
    measurement_type: measurementType,
    timestamp: {
      $gte: startDate,
      $lte: endDate
    }
  }).sort({ timestamp: 1 });
};

// Static method to get emergency readings
biometricDataSchema.statics.getEmergencyReadings = function(userId) {
  return this.find({
    user_id: userId,
    'flags.is_emergency': true
  }).sort({ timestamp: -1 });
};

// Method to check if reading is within normal range
biometricDataSchema.methods.isWithinNormalRange = function() {
  const normalRanges = {
    heart_rate: { min: 60, max: 100 },
    blood_pressure_systolic: { min: 90, max: 140 },
    blood_pressure_diastolic: { min: 60, max: 90 },
    glucose: { min: 70, max: 140 },
    temperature: { min: 36.1, max: 37.2 },
    oxygen_saturation: { min: 95, max: 100 }
  };

  const range = normalRanges[this.measurement_type];
  if (!range) return true; // Unknown measurement type, assume normal

  return this.value >= range.min && this.value <= range.max;
};

// Pre-save middleware to set flags
biometricDataSchema.pre('save', function(next) {
  // Check if reading is within normal range
  if (!this.isWithinNormalRange()) {
    this.flags.requires_attention = true;
    
    // Set emergency flag for critical values
    const criticalRanges = {
      heart_rate: { min: 40, max: 150 },
      blood_pressure_systolic: { min: 70, max: 180 },
      blood_pressure_diastolic: { min: 40, max: 110 },
      glucose: { min: 50, max: 250 },
      temperature: { min: 35, max: 39 },
      oxygen_saturation: { min: 90, max: 100 }
    };

    const criticalRange = criticalRanges[this.measurement_type];
    if (criticalRange && (this.value < criticalRange.min || this.value > criticalRange.max)) {
      this.flags.is_emergency = true;
    }
  }

  next();
});

module.exports = mongoose.model('BiometricData', biometricDataSchema);
