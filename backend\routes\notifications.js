const express = require('express');
const router = express.Router();

// GET /api/notifications - Get user's notifications
router.get('/', async (req, res) => {
  try {
    const notifications = [
      {
        notification_id: 'notif_1',
        notification_type: 'health_alert',
        title: 'High Blood Pressure Detected',
        message: 'Your recent blood pressure reading is above normal range. Consider consulting your doctor.',
        priority: 'high',
        is_read: false,
        created_at: new Date(Date.now() - 2 * 60 * 60 * 1000)
      },
      {
        notification_id: 'notif_2',
        notification_type: 'appointment_reminder',
        title: 'Upcoming Consultation',
        message: 'You have a consultation with Dr. <PERSON> tomorrow at 2:00 PM.',
        priority: 'medium',
        is_read: false,
        created_at: new Date(Date.now() - 24 * 60 * 60 * 1000)
      }
    ];

    res.json({
      success: true,
      message: 'Notifications retrieved successfully',
      data: notifications
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// PUT /api/notifications/:id/read - Mark notification as read
router.put('/:id/read', async (req, res) => {
  try {
    res.json({
      success: true,
      message: 'Notification marked as read'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

module.exports = router;
