import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Text, Card, Chip, ProgressBar } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import LinearGradient from 'react-native-linear-gradient';
import * as Animatable from 'react-native-animatable';
import { useTheme } from '../contexts/ThemeContext';

const PredictionCard = ({ prediction, onPress, style }) => {
  const { theme } = useTheme();

  const getRiskColor = (riskScore) => {
    if (riskScore >= 80) return theme.colors.error;
    if (riskScore >= 60) return theme.colors.warning;
    if (riskScore >= 40) return theme.colors.info;
    return theme.colors.success;
  };

  const getRiskLevel = (riskScore) => {
    if (riskScore >= 80) return 'Very High';
    if (riskScore >= 60) return 'High';
    if (riskScore >= 40) return 'Moderate';
    return 'Low';
  };

  const getPredictionIcon = (type) => {
    const iconMap = {
      disease_risk: 'alert-circle',
      wellness_score: 'heart',
      lifestyle_recommendation: 'lightbulb',
      health_trend: 'trending-up',
      emergency_prediction: 'alert',
    };
    return iconMap[type] || 'brain';
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    });
  };

  const riskColor = getRiskColor(prediction.risk_score);
  const riskLevel = getRiskLevel(prediction.risk_score);

  return (
    <Animatable.View animation="fadeInUp" duration={600}>
      <TouchableOpacity onPress={onPress} activeOpacity={0.8}>
        <Card style={[styles.card, { backgroundColor: theme.colors.surface }, style]}>
          <LinearGradient
            colors={[`${riskColor}10`, `${riskColor}05`]}
            style={styles.gradient}
          >
            <Card.Content style={styles.content}>
              <View style={styles.header}>
                <View style={styles.titleContainer}>
                  <View style={[styles.iconContainer, { backgroundColor: `${riskColor}20` }]}>
                    <Icon 
                      name={getPredictionIcon(prediction.prediction_type)} 
                      size={20} 
                      color={riskColor} 
                    />
                  </View>
                  <View style={styles.titleText}>
                    <Text style={[styles.title, { color: theme.colors.text }]}>
                      {prediction.condition_name}
                    </Text>
                    <Text style={[styles.date, { color: theme.colors.text }]}>
                      {formatDate(prediction.prediction_date)}
                    </Text>
                  </View>
                </View>
                
                <Chip
                  mode="flat"
                  style={[styles.riskChip, { backgroundColor: `${riskColor}20` }]}
                  textStyle={[styles.riskText, { color: riskColor }]}
                >
                  {riskLevel}
                </Chip>
              </View>

              <View style={styles.riskContainer}>
                <View style={styles.riskHeader}>
                  <Text style={[styles.riskLabel, { color: theme.colors.text }]}>
                    Risk Score
                  </Text>
                  <Text style={[styles.riskScore, { color: riskColor }]}>
                    {prediction.risk_score}%
                  </Text>
                </View>
                
                <ProgressBar
                  progress={prediction.risk_score / 100}
                  color={riskColor}
                  style={styles.riskProgress}
                />
              </View>

              <View style={styles.confidenceContainer}>
                <Icon name="check-circle" size={16} color={theme.colors.success} />
                <Text style={[styles.confidence, { color: theme.colors.text }]}>
                  {prediction.confidence_level}% confidence
                </Text>
              </View>

              {prediction.recommendations?.immediate_actions?.length > 0 && (
                <View style={styles.recommendationContainer}>
                  <Text style={[styles.recommendationTitle, { color: theme.colors.text }]}>
                    Recommendation:
                  </Text>
                  <Text style={[styles.recommendationText, { color: theme.colors.text }]}>
                    {prediction.recommendations.immediate_actions[0].action}
                  </Text>
                </View>
              )}

              <View style={styles.footer}>
                <View style={styles.typeContainer}>
                  <Icon name="tag" size={14} color={theme.colors.text} />
                  <Text style={[styles.type, { color: theme.colors.text }]}>
                    {prediction.prediction_type.replace('_', ' ').toUpperCase()}
                  </Text>
                </View>
                
                <Icon name="chevron-right" size={20} color={theme.colors.text} />
              </View>
            </Card.Content>
          </LinearGradient>
        </Card>
      </TouchableOpacity>
    </Animatable.View>
  );
};

const styles = StyleSheet.create({
  card: {
    marginBottom: 16,
    borderRadius: 16,
    elevation: 4,
    overflow: 'hidden',
  },
  gradient: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  titleText: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  date: {
    fontSize: 12,
    opacity: 0.7,
  },
  riskChip: {
    height: 28,
  },
  riskText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  riskContainer: {
    marginBottom: 12,
  },
  riskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  riskLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  riskScore: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  riskProgress: {
    height: 6,
    borderRadius: 3,
  },
  confidenceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  confidence: {
    fontSize: 12,
    marginLeft: 6,
    opacity: 0.8,
  },
  recommendationContainer: {
    marginBottom: 12,
  },
  recommendationTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 4,
    opacity: 0.8,
  },
  recommendationText: {
    fontSize: 14,
    lineHeight: 18,
    opacity: 0.9,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  typeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  type: {
    fontSize: 10,
    marginLeft: 4,
    opacity: 0.6,
    fontWeight: '500',
  },
});

export default PredictionCard;
