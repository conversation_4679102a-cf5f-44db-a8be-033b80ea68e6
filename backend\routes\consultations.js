const express = require('express');
const router = express.Router();

// GET /api/consultations - Get user's consultations
router.get('/', async (req, res) => {
  try {
    const consultations = [
      {
        consultation_id: 'consult_1',
        provider_name: 'Dr. <PERSON>',
        specialization: 'Cardiology',
        appointment_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        status: 'scheduled',
        consultation_type: 'video'
      }
    ];

    res.json({
      success: true,
      message: 'Consultations retrieved successfully',
      data: consultations
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

module.exports = router;
