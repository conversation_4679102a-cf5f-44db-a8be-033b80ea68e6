import React, { useEffect, useState } from 'react';
import {
  StatusBar,
  useColorScheme,
  AppState,
  Alert,
  Linking
} from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { Provider as PaperProvider } from 'react-native-paper';
import FlashMessage from 'react-native-flash-message';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { io } from 'socket.io-client';

// Import contexts
import { AuthProvider } from './src/contexts/AuthContext';
import { ThemeProvider } from './src/contexts/ThemeContext';
import { HealthDataProvider } from './src/contexts/HealthDataContext';

// Import navigation
import AuthNavigator from './src/navigation/AuthNavigator';
import MainNavigator from './src/navigation/MainNavigator';

// Import screens
import SplashScreen from './src/screens/SplashScreen';
import OnboardingScreen from './src/screens/OnboardingScreen';

// Import services
import { authService } from './src/services/authService';
import { notificationService } from './src/services/notificationService';

// Import themes
import { lightTheme, darkTheme } from './src/themes';

// Import utils
import { requestPermissions } from './src/utils/permissions';

const Stack = createStackNavigator();

const App = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [socket, setSocket] = useState(null);
  const systemColorScheme = useColorScheme();
  const [userTheme, setUserTheme] = useState('auto');

  // Determine current theme
  const currentTheme = userTheme === 'auto' 
    ? (systemColorScheme === 'dark' ? darkTheme : lightTheme)
    : (userTheme === 'dark' ? darkTheme : lightTheme);

  useEffect(() => {
    initializeApp();
  }, []);

  useEffect(() => {
    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, []);

  const initializeApp = async () => {
    try {
      // Request necessary permissions
      await requestPermissions();

      // Initialize notification service
      await notificationService.initialize();

      // Check if user has seen onboarding
      const hasSeenOnboarding = await AsyncStorage.getItem('hasSeenOnboarding');
      if (!hasSeenOnboarding) {
        setShowOnboarding(true);
        setIsLoading(false);
        return;
      }

      // Check authentication status
      const token = await AsyncStorage.getItem('authToken');
      if (token) {
        try {
          const isValid = await authService.verifyToken(token);
          if (isValid) {
            setIsAuthenticated(true);
            await initializeSocket(token);
          }
        } catch (error) {
          console.log('Token verification failed:', error);
          await AsyncStorage.removeItem('authToken');
        }
      }

      // Load user theme preference
      const savedTheme = await AsyncStorage.getItem('userTheme');
      if (savedTheme) {
        setUserTheme(savedTheme);
      }

    } catch (error) {
      console.error('App initialization error:', error);
      Alert.alert(
        'Initialization Error',
        'There was an error starting the app. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  const initializeSocket = async (token) => {
    try {
      const socketInstance = io(process.env.REACT_APP_API_URL || 'http://localhost:5000', {
        auth: { token },
        transports: ['websocket']
      });

      socketInstance.on('connect', () => {
        console.log('Socket connected');
        const userId = authService.getCurrentUserId();
        if (userId) {
          socketInstance.emit('join_user_room', userId);
        }
      });

      socketInstance.on('biometric_update', (data) => {
        // Handle real-time biometric updates
        console.log('Biometric update received:', data);
      });

      socketInstance.on('emergency_alert', (data) => {
        // Handle emergency alerts
        Alert.alert(
          'Health Alert',
          `Critical reading detected: ${data.measurement_type} - ${data.value}`,
          [
            { text: 'View Details', onPress: () => handleEmergencyAlert(data) },
            { text: 'OK' }
          ]
        );
      });

      socketInstance.on('notification', (data) => {
        // Handle real-time notifications
        notificationService.showLocalNotification(data);
      });

      socketInstance.on('disconnect', () => {
        console.log('Socket disconnected');
      });

      setSocket(socketInstance);
    } catch (error) {
      console.error('Socket initialization error:', error);
    }
  };

  const handleAppStateChange = (nextAppState) => {
    if (nextAppState === 'active') {
      // App came to foreground
      if (socket && !socket.connected) {
        socket.connect();
      }
    } else if (nextAppState === 'background') {
      // App went to background
      // Keep socket connected for real-time health monitoring
    }
  };

  const handleEmergencyAlert = (data) => {
    // Navigate to emergency screen or take appropriate action
    console.log('Handling emergency alert:', data);
  };

  const handleOnboardingComplete = async () => {
    await AsyncStorage.setItem('hasSeenOnboarding', 'true');
    setShowOnboarding(false);
  };

  const handleLogin = async (token) => {
    await AsyncStorage.setItem('authToken', token);
    setIsAuthenticated(true);
    await initializeSocket(token);
  };

  const handleLogout = async () => {
    await AsyncStorage.removeItem('authToken');
    if (socket) {
      socket.disconnect();
      setSocket(null);
    }
    setIsAuthenticated(false);
  };

  const handleThemeChange = async (theme) => {
    setUserTheme(theme);
    await AsyncStorage.setItem('userTheme', theme);
  };

  if (isLoading) {
    return <SplashScreen />;
  }

  if (showOnboarding) {
    return (
      <PaperProvider theme={currentTheme}>
        <StatusBar
          barStyle={currentTheme.dark ? 'light-content' : 'dark-content'}
          backgroundColor={currentTheme.colors.surface}
        />
        <OnboardingScreen onComplete={handleOnboardingComplete} />
        <FlashMessage position="top" />
      </PaperProvider>
    );
  }

  return (
    <ThemeProvider
      theme={currentTheme}
      userTheme={userTheme}
      onThemeChange={handleThemeChange}
    >
      <AuthProvider
        isAuthenticated={isAuthenticated}
        onLogin={handleLogin}
        onLogout={handleLogout}
      >
        <HealthDataProvider socket={socket}>
          <PaperProvider theme={currentTheme}>
            <StatusBar
              barStyle={currentTheme.dark ? 'light-content' : 'dark-content'}
              backgroundColor={currentTheme.colors.surface}
            />
            <NavigationContainer theme={currentTheme}>
              <Stack.Navigator
                screenOptions={{
                  headerShown: false,
                  gestureEnabled: true,
                  cardStyleInterpolator: ({ current, layouts }) => {
                    return {
                      cardStyle: {
                        transform: [
                          {
                            translateX: current.progress.interpolate({
                              inputRange: [0, 1],
                              outputRange: [layouts.screen.width, 0],
                            }),
                          },
                        ],
                      },
                    };
                  },
                }}
              >
                {isAuthenticated ? (
                  <Stack.Screen name="Main" component={MainNavigator} />
                ) : (
                  <Stack.Screen name="Auth" component={AuthNavigator} />
                )}
              </Stack.Navigator>
            </NavigationContainer>
            <FlashMessage position="top" />
          </PaperProvider>
        </HealthDataProvider>
      </AuthProvider>
    </ThemeProvider>
  );
};

export default App;
